# 订餐系统API接口文档

## 基础信息

- **Base URL**: `http://localhost:8080/api`
- **认证方式**: JWT Bearer <PERSON>
- **数据格式**: JSON
- **字符编码**: UTF-8

## 通用响应格式

### 成功响应
```json
{
  "success": true,
  "message": "操作成功",
  "data": {}, // 具体数据
  "timestamp": "2024-01-01T12:00:00"
}
```

### 错误响应
```json
{
  "success": false,
  "message": "错误信息",
  "error": "详细错误描述",
  "timestamp": "2024-01-01T12:00:00"
}
```

### 分页响应
```json
{
  "success": true,
  "message": "操作成功",
  "data": {
    "content": [], // 数据列表
    "page": 0,     // 当前页码
    "size": 20,    // 每页大小
    "totalElements": 100, // 总记录数
    "totalPages": 5,      // 总页数
    "first": true,        // 是否首页
    "last": false,        // 是否末页
    "empty": false        // 是否为空
  },
  "timestamp": "2024-01-01T12:00:00"
}
```

## 认证接口

### 用户注册
- **URL**: `POST /auth/register`
- **描述**: 新用户注册
- **权限**: 无需认证

**请求参数**:
```json
{
  "username": "testuser",      // 用户名，3-50字符
  "password": "password123",   // 密码，6-100字符
  "confirmPassword": "password123", // 确认密码
  "email": "<EMAIL>", // 邮箱
  "realName": "张三",          // 真实姓名（可选）
  "phone": "13800138000",      // 手机号（可选）
  "address": "北京市朝阳区"     // 地址（可选）
}
```

**响应示例**:
```json
{
  "success": true,
  "message": "注册成功",
  "timestamp": "2024-01-01T12:00:00"
}
```

### 用户登录
- **URL**: `POST /auth/login`
- **描述**: 用户登录获取JWT令牌
- **权限**: 无需认证

**请求参数**:
```json
{
  "usernameOrEmail": "testuser", // 用户名或邮箱
  "password": "password123"      // 密码
}
```

**响应示例**:
```json
{
  "success": true,
  "message": "登录成功",
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "type": "Bearer",
    "id": 1,
    "username": "testuser",
    "email": "<EMAIL>",
    "roles": ["ROLE_CUSTOMER"]
  },
  "timestamp": "2024-01-01T12:00:00"
}
```

### 修改密码
- **URL**: `POST /auth/change-password`
- **描述**: 修改当前用户密码
- **权限**: 需要认证

**请求头**:
```
Authorization: Bearer {JWT_TOKEN}
```

**请求参数**:
```json
{
  "currentPassword": "oldpassword",  // 当前密码
  "newPassword": "newpassword123",   // 新密码
  "confirmNewPassword": "newpassword123" // 确认新密码
}
```

### 忘记密码
- **URL**: `POST /auth/forgot-password`
- **描述**: 发送密码重置邮件
- **权限**: 无需认证

**请求参数**:
```json
{
  "email": "<EMAIL>" // 注册邮箱
}
```

## 用户接口

### 获取个人信息
- **URL**: `GET /users/profile`
- **描述**: 获取当前用户的个人信息
- **权限**: 需要认证

**响应示例**:
```json
{
  "success": true,
  "data": {
    "id": 1,
    "username": "testuser",
    "email": "<EMAIL>",
    "realName": "张三",
    "phone": "13800138000",
    "address": "北京市朝阳区",
    "avatarUrl": null,
    "roles": ["CUSTOMER"],
    "enabled": true,
    "emailVerified": false,
    "lastLoginAt": "2024-01-01T12:00:00",
    "createdAt": "2024-01-01T10:00:00"
  }
}
```

### 更新个人信息
- **URL**: `PUT /users/profile`
- **描述**: 更新当前用户的个人信息
- **权限**: 需要认证

**请求参数**:
```json
{
  "realName": "李四",
  "email": "<EMAIL>",
  "phone": "13900139000",
  "address": "上海市浦东新区",
  "avatarUrl": "http://example.com/avatar.jpg"
}
```

## 餐饮接口

### 获取餐饮列表
- **URL**: `GET /meals`
- **描述**: 分页获取所有可用的餐饮商品
- **权限**: 无需认证

**查询参数**:
- `page`: 页码，从0开始，默认0
- `size`: 每页大小，默认20

**响应示例**:
```json
{
  "success": true,
  "data": {
    "content": [
      {
        "id": 1,
        "merchantId": 1,
        "merchantName": "川味小厨",
        "categoryId": 6,
        "categoryName": "川菜",
        "name": "麻婆豆腐",
        "description": "经典川菜，嫩滑豆腐配麻辣肉末",
        "price": 28.00,
        "originalPrice": null,
        "imageUrl": "/images/meals/mapo-tofu.jpg",
        "status": "AVAILABLE",
        "stock": 100,
        "salesCount": 0,
        "rating": 0.00,
        "ratingCount": 0,
        "tags": ["经典", "下饭", "素食"],
        "spicyLevel": 2,
        "calories": null,
        "preparationTime": 15,
        "enabled": true,
        "createdAt": "2024-01-01T10:00:00"
      }
    ],
    "page": 0,
    "size": 20,
    "totalElements": 5,
    "totalPages": 1
  }
}
```

### 获取餐饮详情
- **URL**: `GET /meals/{id}`
- **描述**: 根据ID获取餐饮商品详细信息
- **权限**: 无需认证

**路径参数**:
- `id`: 餐饮商品ID

### 搜索餐饮
- **URL**: `POST /meals/search`
- **描述**: 根据条件搜索餐饮商品
- **权限**: 无需认证

**请求参数**:
```json
{
  "keyword": "麻婆",           // 关键词（可选）
  "categoryId": 6,            // 分类ID（可选）
  "merchantId": 1,            // 商家ID（可选）
  "minPrice": 10.00,          // 最低价格（可选）
  "maxPrice": 50.00,          // 最高价格（可选）
  "spicyLevel": 2,            // 辣度等级（可选）
  "tags": ["经典", "下饭"],    // 标签（可选）
  "sortBy": "price",          // 排序字段：id, price, rating, salesCount
  "sortDirection": "asc"      // 排序方向：asc, desc
}
```

### 获取热门餐饮
- **URL**: `GET /meals/popular`
- **描述**: 获取销量最高的餐饮商品
- **权限**: 无需认证

**查询参数**:
- `limit`: 返回数量，默认10

## 分类接口

### 获取分类树
- **URL**: `GET /categories/tree`
- **描述**: 获取完整的分类树结构
- **权限**: 无需认证

**响应示例**:
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "name": "中式菜品",
      "description": "传统中式菜品",
      "iconUrl": "/images/categories/chinese.png",
      "sortOrder": 1,
      "enabled": true,
      "children": [
        {
          "id": 6,
          "name": "川菜",
          "description": "四川菜系",
          "sortOrder": 1,
          "enabled": true,
          "parentId": 1,
          "parentName": "中式菜品"
        }
      ]
    }
  ]
}
```

## 订单接口

### 创建订单
- **URL**: `POST /orders`
- **描述**: 客户创建新订单
- **权限**: 需要CUSTOMER角色

**请求参数**:
```json
{
  "merchantId": 1,              // 商家ID
  "orderItems": [               // 订单项列表
    {
      "mealId": 1,              // 商品ID
      "quantity": 2,            // 数量
      "notes": "不要辣"         // 备注（可选）
    }
  ],
  "deliveryAddress": "北京市朝阳区xxx街道", // 配送地址
  "deliveryPhone": "13800138000",         // 配送电话（可选）
  "deliveryContact": "张三",              // 联系人（可选）
  "notes": "尽快配送",                    // 订单备注（可选）
  "paymentMethod": "ONLINE"               // 支付方式：ONLINE, CASH
}
```

**响应示例**:
```json
{
  "success": true,
  "message": "订单创建成功",
  "data": {
    "id": 1,
    "orderNumber": "ORD1704067200000ABC123",
    "customerId": 4,
    "customerName": "王五",
    "merchantId": 1,
    "merchantName": "川味小厨",
    "status": "PENDING",
    "subtotal": 56.00,
    "deliveryFee": 5.00,
    "discountAmount": 0.00,
    "totalAmount": 61.00,
    "deliveryAddress": "北京市朝阳区xxx街道",
    "estimatedDeliveryTime": "2024-01-01T13:30:00",
    "paymentMethod": "ONLINE",
    "paymentStatus": "PENDING",
    "createdAt": "2024-01-01T12:00:00",
    "orderItems": [
      {
        "id": 1,
        "mealId": 1,
        "mealName": "麻婆豆腐",
        "mealImageUrl": "/images/meals/mapo-tofu.jpg",
        "price": 28.00,
        "quantity": 2,
        "subtotal": 56.00,
        "notes": "不要辣"
      }
    ]
  }
}
```

### 获取我的订单
- **URL**: `GET /orders/my`
- **描述**: 获取当前用户的订单列表
- **权限**: 需要CUSTOMER角色

**查询参数**:
- `page`: 页码，从0开始，默认0
- `size`: 每页大小，默认20

### 取消订单
- **URL**: `POST /orders/{id}/cancel`
- **描述**: 客户取消订单
- **权限**: 需要CUSTOMER角色

**查询参数**:
- `reason`: 取消原因，默认"用户主动取消"

## 评分接口

### 创建评分
- **URL**: `POST /ratings`
- **描述**: 客户对商品进行评分
- **权限**: 需要CUSTOMER角色

**请求参数**:
```json
{
  "mealId": 1,                  // 商品ID
  "orderId": 1,                 // 订单ID（可选）
  "score": 4.5,                 // 评分，1.0-5.0
  "review": "味道不错，推荐！",   // 评价内容（可选）
  "anonymous": false            // 是否匿名，默认false
}
```

### 获取我的评分
- **URL**: `GET /ratings/my`
- **描述**: 获取当前用户的评分列表
- **权限**: 需要CUSTOMER角色

### 获取商品评分
- **URL**: `GET /ratings/meal/{mealId}`
- **描述**: 获取指定商品的评分列表
- **权限**: 无需认证

## 错误码说明

| HTTP状态码 | 错误类型 | 说明 |
|-----------|---------|------|
| 400 | Bad Request | 请求参数错误 |
| 401 | Unauthorized | 未认证或认证失败 |
| 403 | Forbidden | 权限不足 |
| 404 | Not Found | 资源不存在 |
| 500 | Internal Server Error | 服务器内部错误 |

## 使用示例

### 完整的下单流程

1. **用户注册/登录**
```bash
# 登录获取token
curl -X POST http://localhost:8080/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"usernameOrEmail": "customer1", "password": "password"}'
```

2. **浏览餐饮**
```bash
# 获取餐饮列表
curl -X GET http://localhost:8080/api/meals
```

3. **创建订单**
```bash
# 使用获取的token创建订单
curl -X POST http://localhost:8080/api/orders \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "merchantId": 1,
    "orderItems": [{"mealId": 1, "quantity": 2}],
    "deliveryAddress": "测试地址"
  }'
```

4. **评价商品**
```bash
# 订单完成后评价
curl -X POST http://localhost:8080/api/ratings \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "mealId": 1,
    "orderId": 1,
    "score": 4.5,
    "review": "很好吃！"
  }'
```

更多详细信息请访问Swagger文档：http://localhost:8080/api/swagger-ui.html
