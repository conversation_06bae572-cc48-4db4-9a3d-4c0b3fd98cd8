package com.foodorder.service;

import com.foodorder.common.exception.BusinessException;
import com.foodorder.dto.auth.*;
import com.foodorder.entity.User;
import com.foodorder.entity.UserRole;
import com.foodorder.repository.UserRepository;
import com.foodorder.security.JwtUtils;
import com.foodorder.security.UserPrincipal;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class AuthService {
    
    private static final Logger logger = LoggerFactory.getLogger(AuthService.class);
    
    @Autowired
    private AuthenticationManager authenticationManager;
    
    @Autowired
    private UserRepository userRepository;
    
    @Autowired
    private PasswordEncoder passwordEncoder;
    
    @Autowired
    private JwtUtils jwtUtils;
    
    @Autowired
    private EmailService emailService;
    
    @Transactional
    public JwtResponse login(LoginRequest loginRequest) {
        Authentication authentication = authenticationManager.authenticate(
            new UsernamePasswordAuthenticationToken(
                loginRequest.getUsernameOrEmail(),
                loginRequest.getPassword()
            )
        );
        
        SecurityContextHolder.getContext().setAuthentication(authentication);
        String jwt = jwtUtils.generateJwtToken(authentication);
        
        UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
        List<String> roles = userPrincipal.getAuthorities().stream()
            .map(item -> item.getAuthority())
            .collect(Collectors.toList());
        
        // 更新最后登录时间
        User user = userRepository.findById(userPrincipal.getId()).orElse(null);
        if (user != null) {
            user.setLastLoginAt(LocalDateTime.now());
            userRepository.save(user);
        }
        
        logger.info("用户登录成功: {}", userPrincipal.getUsername());
        
        return new JwtResponse(jwt, userPrincipal.getId(), userPrincipal.getUsername(), 
                              userPrincipal.getEmail(), roles);
    }
    
    @Transactional
    public void register(RegisterRequest registerRequest) {
        // 验证用户名是否已存在
        if (userRepository.existsByUsername(registerRequest.getUsername())) {
            throw new BusinessException("用户名已存在");
        }
        
        // 验证邮箱是否已存在
        if (userRepository.existsByEmail(registerRequest.getEmail())) {
            throw new BusinessException("邮箱已被注册");
        }
        
        // 验证密码确认
        if (!registerRequest.getPassword().equals(registerRequest.getConfirmPassword())) {
            throw new BusinessException("两次输入的密码不一致");
        }
        
        // 创建新用户
        User user = new User();
        user.setUsername(registerRequest.getUsername());
        user.setPassword(passwordEncoder.encode(registerRequest.getPassword()));
        user.setEmail(registerRequest.getEmail());
        user.setRealName(registerRequest.getRealName());
        user.setPhone(registerRequest.getPhone());
        user.setAddress(registerRequest.getAddress());
        user.getRoles().add(UserRole.CUSTOMER);
        
        userRepository.save(user);
        
        logger.info("新用户注册成功: {}", user.getUsername());
        
        // 发送欢迎邮件
        try {
            emailService.sendWelcomeEmail(user.getEmail(), user.getUsername());
        } catch (Exception e) {
            logger.warn("发送欢迎邮件失败: {}", e.getMessage());
        }
    }
    
    @Transactional
    public void changePassword(Long userId, ChangePasswordRequest request) {
        User user = userRepository.findById(userId)
            .orElseThrow(() -> new BusinessException("用户不存在"));
        
        // 验证当前密码
        if (!passwordEncoder.matches(request.getCurrentPassword(), user.getPassword())) {
            throw new BusinessException("当前密码不正确");
        }
        
        // 验证新密码确认
        if (!request.getNewPassword().equals(request.getConfirmNewPassword())) {
            throw new BusinessException("两次输入的新密码不一致");
        }
        
        // 更新密码
        user.setPassword(passwordEncoder.encode(request.getNewPassword()));
        userRepository.save(user);
        
        logger.info("用户修改密码成功: {}", user.getUsername());
    }
    
    @Transactional
    public void forgotPassword(ForgotPasswordRequest request) {
        User user = userRepository.findByEmail(request.getEmail())
            .orElseThrow(() -> new BusinessException("邮箱不存在"));
        
        // 生成重置令牌
        String resetToken = jwtUtils.generateJwtToken(user.getUsername());
        
        // 发送重置密码邮件
        try {
            emailService.sendPasswordResetEmail(user.getEmail(), user.getUsername(), resetToken);
            logger.info("密码重置邮件已发送: {}", user.getEmail());
        } catch (Exception e) {
            logger.error("发送密码重置邮件失败", e);
            throw new BusinessException("发送重置邮件失败，请稍后重试");
        }
    }
    
    @Transactional
    public void resetPassword(ResetPasswordRequest request) {
        // 验证重置令牌
        if (!jwtUtils.validateJwtToken(request.getToken())) {
            throw new BusinessException("重置令牌无效或已过期");
        }
        
        String username = jwtUtils.getUsernameFromJwtToken(request.getToken());
        User user = userRepository.findByUsername(username)
            .orElseThrow(() -> new BusinessException("用户不存在"));
        
        // 验证新密码确认
        if (!request.getNewPassword().equals(request.getConfirmNewPassword())) {
            throw new BusinessException("两次输入的新密码不一致");
        }
        
        // 更新密码
        user.setPassword(passwordEncoder.encode(request.getNewPassword()));
        userRepository.save(user);
        
        logger.info("用户重置密码成功: {}", user.getUsername());
    }
    
    public User getCurrentUser() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication != null && authentication.getPrincipal() instanceof UserPrincipal) {
            UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
            return userRepository.findById(userPrincipal.getId()).orElse(null);
        }
        return null;
    }
    
    public Long getCurrentUserId() {
        User currentUser = getCurrentUser();
        return currentUser != null ? currentUser.getId() : null;
    }
    
    public boolean hasRole(UserRole role) {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication != null && authentication.getPrincipal() instanceof UserPrincipal) {
            UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
            return userPrincipal.hasRole(role);
        }
        return false;
    }
}
