package com.foodorder.dto.admin;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Map;

public class SystemStatsDto {
    
    // 用户统计
    private Long totalUsers;
    private Long totalCustomers;
    private Long totalMerchants;
    private Long totalAdmins;
    private Long activeUsers; // 最近30天活跃用户
    
    // 商家统计
    private Long totalMerchantsCount;
    private Long approvedMerchants;
    private Long pendingMerchants;
    private Long rejectedMerchants;
    private Long suspendedMerchants;
    
    // 商品统计
    private Long totalMeals;
    private Long availableMeals;
    private Long soldOutMeals;
    private Long discontinuedMeals;
    
    // 订单统计
    private Long totalOrders;
    private Long pendingOrders;
    private Long completedOrders;
    private Long cancelledOrders;
    private BigDecimal totalRevenue;
    private BigDecimal todayRevenue;
    
    // 评分统计
    private Long totalRatings;
    private BigDecimal averageRating;
    private Long ratingsWithReviews;
    
    // 分类统计
    private Long totalCategories;
    private Long rootCategories;
    private Long subCategories;
    
    // 时间统计
    private LocalDateTime lastUpdated;
    
    // 趋势数据
    private Map<String, Long> userRegistrationTrend; // 最近7天注册趋势
    private Map<String, Long> orderTrend; // 最近7天订单趋势
    private Map<String, BigDecimal> revenueTrend; // 最近7天收入趋势
    
    public SystemStatsDto() {
        this.lastUpdated = LocalDateTime.now();
    }
    
    // Getters and Setters
    public Long getTotalUsers() {
        return totalUsers;
    }
    
    public void setTotalUsers(Long totalUsers) {
        this.totalUsers = totalUsers;
    }
    
    public Long getTotalCustomers() {
        return totalCustomers;
    }
    
    public void setTotalCustomers(Long totalCustomers) {
        this.totalCustomers = totalCustomers;
    }
    
    public Long getTotalMerchants() {
        return totalMerchants;
    }
    
    public void setTotalMerchants(Long totalMerchants) {
        this.totalMerchants = totalMerchants;
    }
    
    public Long getTotalAdmins() {
        return totalAdmins;
    }
    
    public void setTotalAdmins(Long totalAdmins) {
        this.totalAdmins = totalAdmins;
    }
    
    public Long getActiveUsers() {
        return activeUsers;
    }
    
    public void setActiveUsers(Long activeUsers) {
        this.activeUsers = activeUsers;
    }
    
    public Long getTotalMerchantsCount() {
        return totalMerchantsCount;
    }
    
    public void setTotalMerchantsCount(Long totalMerchantsCount) {
        this.totalMerchantsCount = totalMerchantsCount;
    }
    
    public Long getApprovedMerchants() {
        return approvedMerchants;
    }
    
    public void setApprovedMerchants(Long approvedMerchants) {
        this.approvedMerchants = approvedMerchants;
    }
    
    public Long getPendingMerchants() {
        return pendingMerchants;
    }
    
    public void setPendingMerchants(Long pendingMerchants) {
        this.pendingMerchants = pendingMerchants;
    }
    
    public Long getRejectedMerchants() {
        return rejectedMerchants;
    }
    
    public void setRejectedMerchants(Long rejectedMerchants) {
        this.rejectedMerchants = rejectedMerchants;
    }
    
    public Long getSuspendedMerchants() {
        return suspendedMerchants;
    }
    
    public void setSuspendedMerchants(Long suspendedMerchants) {
        this.suspendedMerchants = suspendedMerchants;
    }
    
    public Long getTotalMeals() {
        return totalMeals;
    }
    
    public void setTotalMeals(Long totalMeals) {
        this.totalMeals = totalMeals;
    }
    
    public Long getAvailableMeals() {
        return availableMeals;
    }
    
    public void setAvailableMeals(Long availableMeals) {
        this.availableMeals = availableMeals;
    }
    
    public Long getSoldOutMeals() {
        return soldOutMeals;
    }
    
    public void setSoldOutMeals(Long soldOutMeals) {
        this.soldOutMeals = soldOutMeals;
    }
    
    public Long getDiscontinuedMeals() {
        return discontinuedMeals;
    }
    
    public void setDiscontinuedMeals(Long discontinuedMeals) {
        this.discontinuedMeals = discontinuedMeals;
    }
    
    public Long getTotalOrders() {
        return totalOrders;
    }
    
    public void setTotalOrders(Long totalOrders) {
        this.totalOrders = totalOrders;
    }
    
    public Long getPendingOrders() {
        return pendingOrders;
    }
    
    public void setPendingOrders(Long pendingOrders) {
        this.pendingOrders = pendingOrders;
    }
    
    public Long getCompletedOrders() {
        return completedOrders;
    }
    
    public void setCompletedOrders(Long completedOrders) {
        this.completedOrders = completedOrders;
    }
    
    public Long getCancelledOrders() {
        return cancelledOrders;
    }
    
    public void setCancelledOrders(Long cancelledOrders) {
        this.cancelledOrders = cancelledOrders;
    }
    
    public BigDecimal getTotalRevenue() {
        return totalRevenue;
    }
    
    public void setTotalRevenue(BigDecimal totalRevenue) {
        this.totalRevenue = totalRevenue;
    }
    
    public BigDecimal getTodayRevenue() {
        return todayRevenue;
    }
    
    public void setTodayRevenue(BigDecimal todayRevenue) {
        this.todayRevenue = todayRevenue;
    }
    
    public Long getTotalRatings() {
        return totalRatings;
    }
    
    public void setTotalRatings(Long totalRatings) {
        this.totalRatings = totalRatings;
    }
    
    public BigDecimal getAverageRating() {
        return averageRating;
    }
    
    public void setAverageRating(BigDecimal averageRating) {
        this.averageRating = averageRating;
    }
    
    public Long getRatingsWithReviews() {
        return ratingsWithReviews;
    }
    
    public void setRatingsWithReviews(Long ratingsWithReviews) {
        this.ratingsWithReviews = ratingsWithReviews;
    }
    
    public Long getTotalCategories() {
        return totalCategories;
    }
    
    public void setTotalCategories(Long totalCategories) {
        this.totalCategories = totalCategories;
    }
    
    public Long getRootCategories() {
        return rootCategories;
    }
    
    public void setRootCategories(Long rootCategories) {
        this.rootCategories = rootCategories;
    }
    
    public Long getSubCategories() {
        return subCategories;
    }
    
    public void setSubCategories(Long subCategories) {
        this.subCategories = subCategories;
    }
    
    public LocalDateTime getLastUpdated() {
        return lastUpdated;
    }
    
    public void setLastUpdated(LocalDateTime lastUpdated) {
        this.lastUpdated = lastUpdated;
    }
    
    public Map<String, Long> getUserRegistrationTrend() {
        return userRegistrationTrend;
    }
    
    public void setUserRegistrationTrend(Map<String, Long> userRegistrationTrend) {
        this.userRegistrationTrend = userRegistrationTrend;
    }
    
    public Map<String, Long> getOrderTrend() {
        return orderTrend;
    }
    
    public void setOrderTrend(Map<String, Long> orderTrend) {
        this.orderTrend = orderTrend;
    }
    
    public Map<String, BigDecimal> getRevenueTrend() {
        return revenueTrend;
    }
    
    public void setRevenueTrend(Map<String, BigDecimal> revenueTrend) {
        this.revenueTrend = revenueTrend;
    }
}
