package com.foodorder.service;

import com.foodorder.common.exception.ResourceNotFoundException;
import com.foodorder.dto.category.CategoryDto;
import com.foodorder.entity.Category;
import com.foodorder.repository.CategoryRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Service
public class CategoryService {
    
    @Autowired
    private CategoryRepository categoryRepository;
    
    public CategoryDto getCategoryById(Long id) {
        Category category = categoryRepository.findById(id)
            .orElseThrow(() -> new ResourceNotFoundException("分类不存在"));
        return CategoryDto.fromCategory(category);
    }
    
    @Cacheable(value = "categories", key = "'root'")
    public List<CategoryDto> getRootCategories() {
        List<Category> categories = categoryRepository.findRootCategories();
        return categories.stream()
            .map(CategoryDto::fromCategory)
            .collect(Collectors.toList());
    }
    
    @Cacheable(value = "categories", key = "'sub_' + #parentId")
    public List<CategoryDto> getSubCategories(Long parentId) {
        List<Category> categories = categoryRepository.findSubCategories(parentId);
        return categories.stream()
            .map(CategoryDto::fromCategoryWithoutChildren)
            .collect(Collectors.toList());
    }
    
    @Cacheable(value = "categories", key = "'all'")
    public List<CategoryDto> getAllCategories() {
        List<Category> categories = categoryRepository.findByEnabledTrue();
        return categories.stream()
            .map(CategoryDto::fromCategoryWithoutChildren)
            .collect(Collectors.toList());
    }
    
    @Cacheable(value = "categories", key = "'tree'")
    public List<CategoryDto> getCategoryTree() {
        List<Category> rootCategories = categoryRepository.findRootCategories();
        return rootCategories.stream()
            .map(CategoryDto::fromCategory)
            .collect(Collectors.toList());
    }
}
