package com.foodorder.recommendation.abtest;

import com.foodorder.entity.Meal;
import com.foodorder.entity.User;
import com.foodorder.recommendation.RecommendationEngine;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.concurrent.TimeUnit;

/**
 * A/B测试管理器
 */
@Component
public class ABTestManager {
    
    private static final Logger logger = LoggerFactory.getLogger(ABTestManager.class);
    private static final String AB_TEST_PREFIX = "ab_test:";
    private static final String USER_GROUP_PREFIX = "user_group:";
    
    @Autowired
    private RecommendationEngine recommendationEngine;
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    private final Random random = new Random();
    
    /**
     * A/B测试配置
     */
    public static class ABTestConfig {
        private String testName;
        private String controlAlgorithm;
        private String experimentAlgorithm;
        private double trafficSplit; // 实验组流量比例 (0.0 - 1.0)
        private LocalDateTime startTime;
        private LocalDateTime endTime;
        private boolean active;
        
        public ABTestConfig(String testName, String controlAlgorithm, String experimentAlgorithm,
                           double trafficSplit, LocalDateTime startTime, LocalDateTime endTime) {
            this.testName = testName;
            this.controlAlgorithm = controlAlgorithm;
            this.experimentAlgorithm = experimentAlgorithm;
            this.trafficSplit = trafficSplit;
            this.startTime = startTime;
            this.endTime = endTime;
            this.active = true;
        }
        
        // Getters and Setters
        public String getTestName() { return testName; }
        public void setTestName(String testName) { this.testName = testName; }
        
        public String getControlAlgorithm() { return controlAlgorithm; }
        public void setControlAlgorithm(String controlAlgorithm) { this.controlAlgorithm = controlAlgorithm; }
        
        public String getExperimentAlgorithm() { return experimentAlgorithm; }
        public void setExperimentAlgorithm(String experimentAlgorithm) { this.experimentAlgorithm = experimentAlgorithm; }
        
        public double getTrafficSplit() { return trafficSplit; }
        public void setTrafficSplit(double trafficSplit) { this.trafficSplit = trafficSplit; }
        
        public LocalDateTime getStartTime() { return startTime; }
        public void setStartTime(LocalDateTime startTime) { this.startTime = startTime; }
        
        public LocalDateTime getEndTime() { return endTime; }
        public void setEndTime(LocalDateTime endTime) { this.endTime = endTime; }
        
        public boolean isActive() { return active; }
        public void setActive(boolean active) { this.active = active; }
    }
    
    /**
     * 创建A/B测试
     */
    public void createABTest(ABTestConfig config) {
        String testKey = AB_TEST_PREFIX + config.getTestName();
        redisTemplate.opsForValue().set(testKey, config, 30, TimeUnit.DAYS);
        logger.info("创建A/B测试: {}", config.getTestName());
    }
    
    /**
     * 获取用户的推荐算法（基于A/B测试分组）
     */
    public String getUserAlgorithm(User user, String defaultAlgorithm) {
        // 检查是否有活跃的A/B测试
        ABTestConfig activeTest = getActiveABTest();
        if (activeTest == null) {
            return defaultAlgorithm;
        }
        
        // 获取或分配用户组
        String userGroup = getUserGroup(user, activeTest);
        
        if ("experiment".equals(userGroup)) {
            logger.debug("用户 {} 分配到实验组，使用算法: {}", user.getUsername(), activeTest.getExperimentAlgorithm());
            return activeTest.getExperimentAlgorithm();
        } else {
            logger.debug("用户 {} 分配到对照组，使用算法: {}", user.getUsername(), activeTest.getControlAlgorithm());
            return activeTest.getControlAlgorithm();
        }
    }
    
    /**
     * 获取用户推荐（带A/B测试）
     */
    public List<Meal> getRecommendationsWithABTest(User user, int limit, String requestedAlgorithm) {
        String algorithm = getUserAlgorithm(user, requestedAlgorithm);
        
        // 记录A/B测试参与
        recordABTestParticipation(user, algorithm);
        
        // 根据算法获取推荐
        switch (algorithm) {
            case "personalized":
                return recommendationEngine.getPersonalizedRecommendations(user, limit);
            case "collaborative":
                return recommendationEngine.getCollaborativeFilteringRecommendations(user, limit);
            case "content_based":
                return recommendationEngine.getContentBasedRecommendations(user, limit);
            case "hybrid":
                return recommendationEngine.getHybridRecommendations(user, limit);
            case "popular":
                return recommendationEngine.getPopularRecommendations(limit);
            default:
                return recommendationEngine.getPersonalizedRecommendations(user, limit);
        }
    }
    
    /**
     * 记录A/B测试转化
     */
    public void recordABTestConversion(User user, String conversionType, double value) {
        ABTestConfig activeTest = getActiveABTest();
        if (activeTest == null) {
            return;
        }
        
        String userGroup = getUserGroup(user, activeTest);
        String conversionKey = String.format("ab_conversion:%s:%s:%s", 
            activeTest.getTestName(), userGroup, conversionType);
        
        redisTemplate.opsForHash().increment(conversionKey, "count", 1);
        redisTemplate.opsForHash().increment(conversionKey, "value", value);
        redisTemplate.expire(conversionKey, 30, TimeUnit.DAYS);
        
        logger.info("记录A/B测试转化: 测试={}, 用户组={}, 类型={}, 值={}", 
            activeTest.getTestName(), userGroup, conversionType, value);
    }
    
    /**
     * 获取A/B测试结果
     */
    public Map<String, Object> getABTestResults(String testName) {
        Map<String, Object> results = new HashMap<>();
        
        // 获取参与数据
        String controlParticipationKey = "ab_participation:" + testName + ":control";
        String experimentParticipationKey = "ab_participation:" + testName + ":experiment";
        
        Long controlParticipants = (Long) redisTemplate.opsForValue().get(controlParticipationKey);
        Long experimentParticipants = (Long) redisTemplate.opsForValue().get(experimentParticipationKey);
        
        results.put("control_participants", controlParticipants != null ? controlParticipants : 0);
        results.put("experiment_participants", experimentParticipants != null ? experimentParticipants : 0);
        
        // 获取转化数据
        Map<String, Object> controlConversions = redisTemplate.opsForHash().entries("ab_conversion:" + testName + ":control:purchase");
        Map<String, Object> experimentConversions = redisTemplate.opsForHash().entries("ab_conversion:" + testName + ":experiment:purchase");
        
        results.put("control_conversions", controlConversions);
        results.put("experiment_conversions", experimentConversions);
        
        // 计算转化率
        if (controlParticipants != null && controlParticipants > 0) {
            Long controlConversionCount = (Long) controlConversions.get("count");
            if (controlConversionCount != null) {
                double controlConversionRate = (double) controlConversionCount / controlParticipants;
                results.put("control_conversion_rate", controlConversionRate);
            }
        }
        
        if (experimentParticipants != null && experimentParticipants > 0) {
            Long experimentConversionCount = (Long) experimentConversions.get("count");
            if (experimentConversionCount != null) {
                double experimentConversionRate = (double) experimentConversionCount / experimentParticipants;
                results.put("experiment_conversion_rate", experimentConversionRate);
            }
        }
        
        return results;
    }
    
    /**
     * 停止A/B测试
     */
    public void stopABTest(String testName) {
        String testKey = AB_TEST_PREFIX + testName;
        ABTestConfig config = (ABTestConfig) redisTemplate.opsForValue().get(testKey);
        if (config != null) {
            config.setActive(false);
            redisTemplate.opsForValue().set(testKey, config, 30, TimeUnit.DAYS);
            logger.info("停止A/B测试: {}", testName);
        }
    }
    
    private ABTestConfig getActiveABTest() {
        // 简化实现：假设只有一个活跃测试
        // 实际应用中可能需要支持多个并行测试
        String testKey = AB_TEST_PREFIX + "recommendation_algorithm_test";
        ABTestConfig config = (ABTestConfig) redisTemplate.opsForValue().get(testKey);
        
        if (config != null && config.isActive()) {
            LocalDateTime now = LocalDateTime.now();
            if (now.isAfter(config.getStartTime()) && now.isBefore(config.getEndTime())) {
                return config;
            }
        }
        
        return null;
    }
    
    private String getUserGroup(User user, ABTestConfig config) {
        String userGroupKey = USER_GROUP_PREFIX + config.getTestName() + ":" + user.getId();
        String userGroup = (String) redisTemplate.opsForValue().get(userGroupKey);
        
        if (userGroup == null) {
            // 基于用户ID的哈希值进行分组，确保一致性
            double hash = Math.abs(user.getId().hashCode()) % 1000 / 1000.0;
            userGroup = hash < config.getTrafficSplit() ? "experiment" : "control";
            
            redisTemplate.opsForValue().set(userGroupKey, userGroup, 30, TimeUnit.DAYS);
            logger.debug("为用户 {} 分配A/B测试组: {}", user.getUsername(), userGroup);
        }
        
        return userGroup;
    }
    
    private void recordABTestParticipation(User user, String algorithm) {
        ABTestConfig activeTest = getActiveABTest();
        if (activeTest == null) {
            return;
        }
        
        String userGroup = getUserGroup(user, activeTest);
        String participationKey = "ab_participation:" + activeTest.getTestName() + ":" + userGroup;
        
        redisTemplate.opsForValue().increment(participationKey);
        redisTemplate.expire(participationKey, 30, TimeUnit.DAYS);
    }
}
