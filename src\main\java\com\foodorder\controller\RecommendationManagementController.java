package com.foodorder.controller;

import com.foodorder.common.response.ApiResponse;
import com.foodorder.recommendation.abtest.ABTestManager;
import com.foodorder.recommendation.config.RecommendationConfig;
import com.foodorder.recommendation.evaluation.RecommendationEvaluator;
import com.foodorder.recommendation.logging.RecommendationLogger;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/admin/recommendations")
@PreAuthorize("hasRole('ADMIN')")
@Tag(name = "推荐系统管理", description = "推荐系统管理相关接口")
public class RecommendationManagementController {
    
    @Autowired
    private ABTestManager abTestManager;
    
    @Autowired
    private RecommendationLogger recommendationLogger;
    
    @Autowired
    private RecommendationEvaluator recommendationEvaluator;
    
    @Autowired
    private RecommendationConfig recommendationConfig;
    
    // A/B测试管理
    @PostMapping("/ab-tests")
    @Operation(summary = "创建A/B测试", description = "创建新的推荐算法A/B测试")
    public ResponseEntity<ApiResponse<Void>> createABTest(
            @RequestParam String testName,
            @RequestParam String controlAlgorithm,
            @RequestParam String experimentAlgorithm,
            @RequestParam double trafficSplit,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startTime,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endTime) {
        
        ABTestManager.ABTestConfig config = new ABTestManager.ABTestConfig(
            testName, controlAlgorithm, experimentAlgorithm, trafficSplit, startTime, endTime);
        
        abTestManager.createABTest(config);
        return ResponseEntity.ok(ApiResponse.success("A/B测试创建成功"));
    }
    
    @GetMapping("/ab-tests/{testName}/results")
    @Operation(summary = "获取A/B测试结果", description = "获取指定A/B测试的结果数据")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getABTestResults(@PathVariable String testName) {
        Map<String, Object> results = abTestManager.getABTestResults(testName);
        return ResponseEntity.ok(ApiResponse.success(results));
    }
    
    @PostMapping("/ab-tests/{testName}/stop")
    @Operation(summary = "停止A/B测试", description = "停止指定的A/B测试")
    public ResponseEntity<ApiResponse<Void>> stopABTest(@PathVariable String testName) {
        abTestManager.stopABTest(testName);
        return ResponseEntity.ok(ApiResponse.success("A/B测试已停止"));
    }
    
    // 推荐日志管理
    @GetMapping("/logs/users/{userId}")
    @Operation(summary = "获取用户推荐日志", description = "获取指定用户的推荐历史记录")
    public ResponseEntity<ApiResponse<List<RecommendationLogger.RecommendationLogEntry>>> getUserRecommendationLogs(
            @PathVariable Long userId,
            @RequestParam(defaultValue = "50") int limit) {
        
        List<RecommendationLogger.RecommendationLogEntry> logs = 
            recommendationLogger.getUserRecommendationHistory(userId, limit);
        return ResponseEntity.ok(ApiResponse.success(logs));
    }
    
    @GetMapping("/stats/algorithms/{algorithm}")
    @Operation(summary = "获取算法统计", description = "获取指定推荐算法的统计信息")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getAlgorithmStats(@PathVariable String algorithm) {
        Map<String, Object> stats = recommendationLogger.getAlgorithmStats(algorithm);
        return ResponseEntity.ok(ApiResponse.success(stats));
    }
    
    // 推荐配置管理
    @GetMapping("/config")
    @Operation(summary = "获取推荐配置", description = "获取当前推荐系统配置")
    public ResponseEntity<ApiResponse<RecommendationConfig>> getRecommendationConfig() {
        return ResponseEntity.ok(ApiResponse.success(recommendationConfig));
    }
    
    @PutMapping("/config/personalized-weights")
    @Operation(summary = "更新个性化权重", description = "更新个性化推荐算法的权重配置")
    public ResponseEntity<ApiResponse<Void>> updatePersonalizedWeights(
            @RequestBody RecommendationConfig.PersonalizedWeights weights) {
        
        recommendationConfig.setPersonalizedWeights(weights);
        return ResponseEntity.ok(ApiResponse.success("个性化权重配置已更新"));
    }
    
    @PutMapping("/config/similarity-weights")
    @Operation(summary = "更新相似度权重", description = "更新相似度计算的权重配置")
    public ResponseEntity<ApiResponse<Void>> updateSimilarityWeights(
            @RequestBody RecommendationConfig.SimilarityWeights weights) {
        
        recommendationConfig.setSimilarityWeights(weights);
        return ResponseEntity.ok(ApiResponse.success("相似度权重配置已更新"));
    }
    
    @PutMapping("/config/algorithm")
    @Operation(summary = "更新算法配置", description = "更新推荐算法的基础配置")
    public ResponseEntity<ApiResponse<Void>> updateAlgorithmConfig(
            @RequestBody RecommendationConfig.AlgorithmConfig config) {
        
        recommendationConfig.setAlgorithmConfig(config);
        return ResponseEntity.ok(ApiResponse.success("算法配置已更新"));
    }
    
    // 推荐效果分析
    @PostMapping("/evaluation/precision")
    @Operation(summary = "计算推荐准确率", description = "计算指定用户和推荐列表的准确率")
    public ResponseEntity<ApiResponse<Double>> calculatePrecision(
            @RequestParam Long userId,
            @RequestBody List<Long> recommendedMealIds,
            @RequestBody List<Long> actualPurchasedMealIds) {
        
        // 这里需要实现具体的计算逻辑
        // 由于需要Meal和User对象，这里简化处理
        return ResponseEntity.ok(ApiResponse.success(0.0));
    }
    
    @GetMapping("/performance/summary")
    @Operation(summary = "获取推荐性能摘要", description = "获取推荐系统整体性能摘要")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getPerformanceSummary() {
        // 获取各算法的统计信息
        Map<String, Object> summary = Map.of(
            "personalized", recommendationLogger.getAlgorithmStats("personalized"),
            "collaborative", recommendationLogger.getAlgorithmStats("collaborative"),
            "content_based", recommendationLogger.getAlgorithmStats("content_based"),
            "hybrid", recommendationLogger.getAlgorithmStats("hybrid"),
            "popular", recommendationLogger.getAlgorithmStats("popular")
        );
        
        return ResponseEntity.ok(ApiResponse.success(summary));
    }
    
    // 推荐系统健康检查
    @GetMapping("/health")
    @Operation(summary = "推荐系统健康检查", description = "检查推荐系统各组件的健康状态")
    public ResponseEntity<ApiResponse<Map<String, String>>> healthCheck() {
        Map<String, String> health = Map.of(
            "recommendation_engine", "healthy",
            "ab_test_manager", "healthy",
            "recommendation_logger", "healthy",
            "cache_system", "healthy",
            "evaluation_system", "healthy"
        );
        
        return ResponseEntity.ok(ApiResponse.success(health));
    }
    
    // 缓存管理
    @PostMapping("/cache/clear")
    @Operation(summary = "清除推荐缓存", description = "清除所有推荐相关的缓存")
    public ResponseEntity<ApiResponse<Void>> clearRecommendationCache() {
        // 这里需要实现缓存清除逻辑
        return ResponseEntity.ok(ApiResponse.success("推荐缓存已清除"));
    }
    
    @PostMapping("/cache/warm-up")
    @Operation(summary = "预热推荐缓存", description = "预热常用的推荐缓存")
    public ResponseEntity<ApiResponse<Void>> warmUpCache() {
        // 这里需要实现缓存预热逻辑
        return ResponseEntity.ok(ApiResponse.success("推荐缓存预热完成"));
    }
}
