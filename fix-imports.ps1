# PowerShell script to replace jakarta imports with javax imports for Spring Boot 2.7.18 compatibility

Write-Host "Starting to fix Jakarta imports to Javax imports..."

# Get all Java files in the src directory
$javaFiles = Get-ChildItem -Path "src" -Filter "*.java" -Recurse

foreach ($file in $javaFiles) {
    Write-Host "Processing: $($file.FullName)"

    # Read the file content as array of lines
    $lines = Get-Content -Path $file.FullName

    # Process each line
    $modifiedLines = @()
    foreach ($line in $lines) {
        # Replace jakarta imports with javax imports
        $modifiedLine = $line -replace 'import jakarta\.persistence', 'import javax.persistence'
        $modifiedLine = $modifiedLine -replace 'import jakarta\.validation', 'import javax.validation'
        $modifiedLine = $modifiedLine -replace 'import jakarta\.servlet', 'import javax.servlet'
        $modifiedLines += $modifiedLine
    }

    # Write the modified content back to the file
    Set-Content -Path $file.FullName -Value $modifiedLines
}

Write-Host "Finished fixing imports!"
