package com.foodorder.recommendation.impl;

import com.foodorder.entity.*;
import com.foodorder.recommendation.RecommendationEngine;
import com.foodorder.recommendation.config.RecommendationConfig;
import com.foodorder.recommendation.logging.RecommendationLogger;
import com.foodorder.repository.*;
import com.foodorder.service.RatingService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Component
public class DefaultRecommendationEngine implements RecommendationEngine {
    
    private static final Logger logger = LoggerFactory.getLogger(DefaultRecommendationEngine.class);
    
    @Autowired
    private MealRepository mealRepository;
    
    @Autowired
    private RatingRepository ratingRepository;
    
    @Autowired
    private OrderRepository orderRepository;
    
    @Autowired
    private CategoryRepository categoryRepository;
    
    @Autowired
    private RatingService ratingService;

    @Autowired
    private RecommendationConfig config;

    @Autowired
    private RecommendationLogger recommendationLogger;
    
    @Override
    @Cacheable(value = "recommendations", key = "'personalized_' + #user.id + '_' + #limit")
    public List<Meal> getPersonalizedRecommendations(User user, int limit) {
        long startTime = System.currentTimeMillis();
        logger.info("生成用户 {} 的个性化推荐", user.getUsername());

        try {
            // 获取用户的评分历史
            List<Rating> userRatings = ratingRepository.findByUserOrderByCreatedAtDesc(user);

            if (userRatings.isEmpty()) {
                // 新用户，返回热门推荐
                List<Meal> recommendations = getPopularRecommendations(limit);
                long executionTime = System.currentTimeMillis() - startTime;
                recommendationLogger.logRecommendationRequest(user, "personalized_fallback_popular",
                    limit, recommendations, executionTime);
                return recommendations;
            }
        
        // 分析用户偏好
        Map<Long, Double> categoryPreferences = analyzeUserCategoryPreferences(userRatings);
        Set<String> preferredTags = analyzeUserTagPreferences(userRatings);
        
        // 获取候选商品
        List<Meal> candidates = mealRepository.findAvailableMeals();
        
        // 过滤掉用户已评分的商品
        Set<Long> ratedMealIds = userRatings.stream()
            .map(rating -> rating.getMeal().getId())
            .collect(Collectors.toSet());
        
        candidates = candidates.stream()
            .filter(meal -> !ratedMealIds.contains(meal.getId()))
            .collect(Collectors.toList());
        
        // 计算推荐分数
        List<MealScore> scoredMeals = candidates.stream()
            .map(meal -> new MealScore(meal, calculatePersonalizedScore(meal, categoryPreferences, preferredTags)))
            .sorted((a, b) -> Double.compare(b.score, a.score))
            .limit(limit)
            .collect(Collectors.toList());
        
            List<Meal> recommendations = scoredMeals.stream()
                .map(ms -> ms.meal)
                .collect(Collectors.toList());

            long executionTime = System.currentTimeMillis() - startTime;
            recommendationLogger.logRecommendationRequest(user, "personalized",
                limit, recommendations, executionTime);

            return recommendations;
        } catch (Exception e) {
            logger.error("个性化推荐生成失败", e);
            return getPopularRecommendations(limit);
        }
    }
    
    @Override
    @Cacheable(value = "recommendations", key = "'realtime_' + #user.id + '_' + #limit")
    public List<Meal> getRealtimeRecommendations(User user, int limit) {
        logger.info("生成用户 {} 的实时推荐", user.getUsername());
        
        // 获取用户最近的评分（最近7天）
        LocalDateTime since = LocalDateTime.now().minusDays(7);
        List<Rating> recentRatings = ratingRepository.findRecentRatingsByUser(user, since);
        
        if (recentRatings.isEmpty()) {
            return getPersonalizedRecommendations(user, limit);
        }
        
        // 基于最近评分的商品找相似商品
        List<Meal> recommendations = new ArrayList<>();
        
        for (Rating rating : recentRatings) {
            if (rating.getScore().compareTo(BigDecimal.valueOf(4.0)) >= 0) { // 高分商品
                List<Meal> similarMeals = getSimilarMeals(rating.getMeal(), 3);
                recommendations.addAll(similarMeals);
            }
        }
        
        // 去重并限制数量
        return recommendations.stream()
            .distinct()
            .limit(limit)
            .collect(Collectors.toList());
    }
    
    @Override
    @Cacheable(value = "recommendations", key = "'similar_' + #meal.id + '_' + #limit")
    public List<Meal> getSimilarMeals(Meal meal, int limit) {
        logger.info("查找与商品 {} 相似的商品", meal.getName());
        
        List<Meal> candidates = mealRepository.findAvailableMeals();
        
        // 移除自身
        candidates = candidates.stream()
            .filter(m -> !m.getId().equals(meal.getId()))
            .collect(Collectors.toList());
        
        // 计算相似度分数
        List<MealScore> scoredMeals = candidates.stream()
            .map(candidate -> new MealScore(candidate, calculateSimilarityScore(meal, candidate)))
            .sorted((a, b) -> Double.compare(b.score, a.score))
            .limit(limit)
            .collect(Collectors.toList());
        
        return scoredMeals.stream()
            .map(ms -> ms.meal)
            .collect(Collectors.toList());
    }
    
    @Override
    @Cacheable(value = "recommendations", key = "'popular_' + #limit")
    public List<Meal> getPopularRecommendations(int limit) {
        logger.info("获取热门推荐，数量: {}", limit);
        
        Pageable pageable = PageRequest.of(0, limit);
        return mealRepository.findPopularMeals(pageable);
    }
    
    @Override
    public List<Meal> getCategoryBasedRecommendations(User user, Long categoryId, int limit) {
        Category category = categoryRepository.findById(categoryId).orElse(null);
        if (category == null) {
            return Collections.emptyList();
        }
        
        Pageable pageable = PageRequest.of(0, limit);
        return mealRepository.findAvailableMealsByCategory(category, pageable).getContent();
    }
    
    @Override
    public List<Meal> getCollaborativeFilteringRecommendations(User user, int limit) {
        logger.info("生成用户 {} 的协同过滤推荐", user.getUsername());
        
        // 获取用户的评分
        List<Rating> userRatings = ratingRepository.findByUser(user);
        if (userRatings.isEmpty()) {
            return getPopularRecommendations(limit);
        }
        
        // 找到相似用户
        List<User> similarUsers = findSimilarUsers(user, userRatings);
        
        // 获取相似用户喜欢但当前用户未评分的商品
        Set<Long> userRatedMealIds = userRatings.stream()
            .map(rating -> rating.getMeal().getId())
            .collect(Collectors.toSet());
        
        Map<Long, Double> mealScores = new HashMap<>();
        
        for (User similarUser : similarUsers) {
            List<Rating> similarUserRatings = ratingRepository.findByUser(similarUser);
            for (Rating rating : similarUserRatings) {
                Long mealId = rating.getMeal().getId();
                if (!userRatedMealIds.contains(mealId) && rating.getScore().compareTo(BigDecimal.valueOf(4.0)) >= 0) {
                    mealScores.merge(mealId, rating.getScore().doubleValue(), Double::sum);
                }
            }
        }
        
        // 按分数排序并获取推荐
        return mealScores.entrySet().stream()
            .sorted(Map.Entry.<Long, Double>comparingByValue().reversed())
            .limit(limit)
            .map(entry -> mealRepository.findById(entry.getKey()).orElse(null))
            .filter(Objects::nonNull)
            .collect(Collectors.toList());
    }
    
    @Override
    public List<Meal> getContentBasedRecommendations(User user, int limit) {
        logger.info("生成用户 {} 的内容推荐", user.getUsername());
        
        // 获取用户高分评价的商品
        List<Rating> highRatings = ratingRepository.findByUser(user).stream()
            .filter(rating -> rating.getScore().compareTo(BigDecimal.valueOf(4.0)) >= 0)
            .collect(Collectors.toList());
        
        if (highRatings.isEmpty()) {
            return getPopularRecommendations(limit);
        }
        
        // 分析用户偏好的特征
        Map<Long, Double> categoryPreferences = analyzeUserCategoryPreferences(highRatings);
        Set<String> preferredTags = analyzeUserTagPreferences(highRatings);
        
        // 获取候选商品
        List<Meal> candidates = mealRepository.findAvailableMeals();
        
        // 过滤已评分商品
        Set<Long> ratedMealIds = ratingRepository.findByUser(user).stream()
            .map(rating -> rating.getMeal().getId())
            .collect(Collectors.toSet());
        
        candidates = candidates.stream()
            .filter(meal -> !ratedMealIds.contains(meal.getId()))
            .collect(Collectors.toList());
        
        // 计算内容相似度分数
        List<MealScore> scoredMeals = candidates.stream()
            .map(meal -> new MealScore(meal, calculateContentScore(meal, categoryPreferences, preferredTags)))
            .sorted((a, b) -> Double.compare(b.score, a.score))
            .limit(limit)
            .collect(Collectors.toList());
        
        return scoredMeals.stream()
            .map(ms -> ms.meal)
            .collect(Collectors.toList());
    }
    
    @Override
    public List<Meal> getHybridRecommendations(User user, int limit) {
        logger.info("生成用户 {} 的混合推荐", user.getUsername());
        
        // 获取不同算法的推荐结果
        List<Meal> personalizedRecs = getPersonalizedRecommendations(user, limit / 2);
        List<Meal> collaborativeRecs = getCollaborativeFilteringRecommendations(user, limit / 2);
        List<Meal> contentRecs = getContentBasedRecommendations(user, limit / 2);
        List<Meal> popularRecs = getPopularRecommendations(limit / 4);
        
        // 合并并去重
        Set<Meal> hybridSet = new LinkedHashSet<>();
        hybridSet.addAll(personalizedRecs);
        hybridSet.addAll(collaborativeRecs);
        hybridSet.addAll(contentRecs);
        hybridSet.addAll(popularRecs);
        
        return hybridSet.stream()
            .limit(limit)
            .collect(Collectors.toList());
    }
    
    // 辅助方法
    private Map<Long, Double> analyzeUserCategoryPreferences(List<Rating> ratings) {
        Map<Long, List<Double>> categoryRatings = new HashMap<>();
        
        for (Rating rating : ratings) {
            Long categoryId = rating.getMeal().getCategory().getId();
            categoryRatings.computeIfAbsent(categoryId, k -> new ArrayList<>())
                .add(rating.getScore().doubleValue());
        }
        
        Map<Long, Double> preferences = new HashMap<>();
        for (Map.Entry<Long, List<Double>> entry : categoryRatings.entrySet()) {
            double avgRating = entry.getValue().stream()
                .mapToDouble(Double::doubleValue)
                .average()
                .orElse(0.0);
            preferences.put(entry.getKey(), avgRating);
        }
        
        return preferences;
    }
    
    private Set<String> analyzeUserTagPreferences(List<Rating> ratings) {
        Map<String, List<Double>> tagRatings = new HashMap<>();
        
        for (Rating rating : ratings) {
            for (String tag : rating.getMeal().getTags()) {
                tagRatings.computeIfAbsent(tag, k -> new ArrayList<>())
                    .add(rating.getScore().doubleValue());
            }
        }
        
        return tagRatings.entrySet().stream()
            .filter(entry -> {
                double avgRating = entry.getValue().stream()
                    .mapToDouble(Double::doubleValue)
                    .average()
                    .orElse(0.0);
                return avgRating >= 4.0;
            })
            .map(Map.Entry::getKey)
            .collect(Collectors.toSet());
    }
    
    private double calculatePersonalizedScore(Meal meal, Map<Long, Double> categoryPreferences, Set<String> preferredTags) {
        double score = 0.0;
        RecommendationConfig.PersonalizedWeights weights = config.getPersonalizedWeights();

        // 分类偏好分数
        Long categoryId = meal.getCategory().getId();
        if (categoryPreferences.containsKey(categoryId)) {
            score += categoryPreferences.get(categoryId) * weights.getCategoryPreference();
        }

        // 标签偏好分数
        long matchingTags = meal.getTags().stream()
            .filter(preferredTags::contains)
            .count();
        score += (matchingTags / (double) Math.max(meal.getTags().size(), 1)) * weights.getTagPreference();

        // 商品评分
        score += meal.getRating().doubleValue() * weights.getRatingWeight();

        // 销量因子
        score += Math.log(meal.getSalesCount() + 1) * weights.getSalesWeight();

        return score;
    }
    
    private double calculateSimilarityScore(Meal meal1, Meal meal2) {
        double score = 0.0;
        RecommendationConfig.SimilarityWeights weights = config.getSimilarityWeights();

        // 分类相似度
        if (meal1.getCategory().getId().equals(meal2.getCategory().getId())) {
            score += weights.getCategoryWeight();
        }

        // 标签相似度
        Set<String> tags1 = meal1.getTags();
        Set<String> tags2 = meal2.getTags();
        Set<String> intersection = new HashSet<>(tags1);
        intersection.retainAll(tags2);
        Set<String> union = new HashSet<>(tags1);
        union.addAll(tags2);

        if (!union.isEmpty()) {
            score += (intersection.size() / (double) union.size()) * weights.getTagWeight();
        }

        // 价格相似度
        double priceDiff = Math.abs(meal1.getPrice().doubleValue() - meal2.getPrice().doubleValue());
        double maxPrice = Math.max(meal1.getPrice().doubleValue(), meal2.getPrice().doubleValue());
        if (maxPrice > 0) {
            score += (1 - priceDiff / maxPrice) * weights.getPriceWeight();
        }

        // 评分相似度
        double ratingDiff = Math.abs(meal1.getRating().doubleValue() - meal2.getRating().doubleValue());
        score += (1 - ratingDiff / 5.0) * weights.getRatingWeight();

        return score;
    }
    
    private double calculateContentScore(Meal meal, Map<Long, Double> categoryPreferences, Set<String> preferredTags) {
        return calculatePersonalizedScore(meal, categoryPreferences, preferredTags);
    }
    
    private List<User> findSimilarUsers(User user, List<Rating> userRatings) {
        // 简化的相似用户查找算法
        Map<Long, Double> userMealRatings = userRatings.stream()
            .collect(Collectors.toMap(
                rating -> rating.getMeal().getId(),
                rating -> rating.getScore().doubleValue()
            ));
        
        List<User> allUsers = ratingRepository.findAll().stream()
            .map(Rating::getUser)
            .distinct()
            .filter(u -> !u.getId().equals(user.getId()))
            .collect(Collectors.toList());
        
        List<UserSimilarity> similarities = new ArrayList<>();
        
        for (User otherUser : allUsers) {
            List<Rating> otherRatings = ratingRepository.findByUser(otherUser);
            double similarity = calculateUserSimilarity(userMealRatings, otherRatings);
            if (similarity > 0.1) { // 相似度阈值
                similarities.add(new UserSimilarity(otherUser, similarity));
            }
        }
        
        return similarities.stream()
            .sorted((a, b) -> Double.compare(b.similarity, a.similarity))
            .limit(10) // 取前10个相似用户
            .map(us -> us.user)
            .collect(Collectors.toList());
    }
    
    private double calculateUserSimilarity(Map<Long, Double> userRatings, List<Rating> otherRatings) {
        Map<Long, Double> otherUserRatings = otherRatings.stream()
            .collect(Collectors.toMap(
                rating -> rating.getMeal().getId(),
                rating -> rating.getScore().doubleValue()
            ));
        
        Set<Long> commonMeals = new HashSet<>(userRatings.keySet());
        commonMeals.retainAll(otherUserRatings.keySet());
        
        if (commonMeals.isEmpty()) {
            return 0.0;
        }
        
        // 计算皮尔逊相关系数
        double sum1 = 0, sum2 = 0, sum1Sq = 0, sum2Sq = 0, pSum = 0;
        int n = commonMeals.size();
        
        for (Long mealId : commonMeals) {
            double rating1 = userRatings.get(mealId);
            double rating2 = otherUserRatings.get(mealId);
            
            sum1 += rating1;
            sum2 += rating2;
            sum1Sq += rating1 * rating1;
            sum2Sq += rating2 * rating2;
            pSum += rating1 * rating2;
        }
        
        double num = pSum - (sum1 * sum2 / n);
        double den = Math.sqrt((sum1Sq - sum1 * sum1 / n) * (sum2Sq - sum2 * sum2 / n));
        
        return den == 0 ? 0 : num / den;
    }
    
    // 内部类
    private static class MealScore {
        final Meal meal;
        final double score;
        
        MealScore(Meal meal, double score) {
            this.meal = meal;
            this.score = score;
        }
    }
    
    private static class UserSimilarity {
        final User user;
        final double similarity;
        
        UserSimilarity(User user, double similarity) {
            this.user = user;
            this.similarity = similarity;
        }
    }
}
