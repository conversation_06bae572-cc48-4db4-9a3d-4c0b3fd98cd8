package com.foodorder.entity;

public enum UserRole {
    CUSTOMER("订餐人"),
    MERCHANT("商家"),
    ADMIN("管理员");
    
    private final String description;
    
    UserRole(String description) {
        this.description = description;
    }
    
    public String getDescription() {
        return description;
    }
    
    public String getAuthority() {
        return "ROLE_" + this.name();
    }
}
