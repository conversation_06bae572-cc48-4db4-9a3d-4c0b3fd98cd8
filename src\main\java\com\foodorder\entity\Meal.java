package com.foodorder.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

import java.math.BigDecimal;
import java.util.HashSet;
import java.util.Set;

@Entity
@Table(name = "meals", indexes = {
    @Index(name = "idx_meal_merchant_id", columnList = "merchant_id"),
    @Index(name = "idx_meal_category_id", columnList = "category_id"),
    @Index(name = "idx_meal_status", columnList = "status"),
    @Index(name = "idx_meal_rating", columnList = "rating"),
    @Index(name = "idx_meal_sales_count", columnList = "sales_count")
})
public class Meal extends BaseEntity {
    
    @NotNull(message = "商家不能为空")
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "merchant_id", nullable = false)
    private Merchant merchant;
    
    @NotNull(message = "分类不能为空")
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "category_id", nullable = false)
    private Category category;
    
    @NotBlank(message = "商品名称不能为空")
    @Column(name = "name", nullable = false, length = 100)
    private String name;
    
    @Column(name = "description", columnDefinition = "TEXT")
    private String description;
    
    @NotNull(message = "价格不能为空")
    @DecimalMin(value = "0.01", message = "价格必须大于0")
    @Column(name = "price", nullable = false, precision = 10, scale = 2)
    private BigDecimal price;
    
    @Column(name = "original_price", precision = 10, scale = 2)
    private BigDecimal originalPrice;
    
    @Column(name = "image_url")
    private String imageUrl;
    
    @ElementCollection
    @CollectionTable(name = "meal_images", joinColumns = @JoinColumn(name = "meal_id"))
    @Column(name = "image_url")
    private Set<String> imageUrls = new HashSet<>();
    
    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    private MealStatus status = MealStatus.AVAILABLE;
    
    @Column(name = "stock", nullable = false)
    private Integer stock = 0;
    
    @Column(name = "sales_count", nullable = false)
    private Integer salesCount = 0;
    
    @Column(name = "rating", precision = 3, scale = 2)
    private BigDecimal rating = BigDecimal.ZERO;
    
    @Column(name = "rating_count", nullable = false)
    private Integer ratingCount = 0;
    
    @Column(name = "sort_order", nullable = false)
    private Integer sortOrder = 0;
    
    @ElementCollection
    @CollectionTable(name = "meal_tags", joinColumns = @JoinColumn(name = "meal_id"))
    @Column(name = "tag")
    private Set<String> tags = new HashSet<>();
    
    @Column(name = "spicy_level")
    private Integer spicyLevel = 0; // 0-不辣, 1-微辣, 2-中辣, 3-重辣
    
    @Column(name = "calories")
    private Integer calories;
    
    @Column(name = "preparation_time")
    private Integer preparationTime; // 制作时间（分钟）
    
    @Column(name = "enabled", nullable = false)
    private Boolean enabled = true;
    
    // 构造函数
    public Meal() {}
    
    public Meal(Merchant merchant, Category category, String name, BigDecimal price) {
        this.merchant = merchant;
        this.category = category;
        this.name = name;
        this.price = price;
    }
    
    // 便利方法
    public void updateRating(BigDecimal newRating) {
        if (this.ratingCount == 0) {
            this.rating = newRating;
            this.ratingCount = 1;
        } else {
            BigDecimal totalRating = this.rating.multiply(BigDecimal.valueOf(this.ratingCount));
            totalRating = totalRating.add(newRating);
            this.ratingCount++;
            this.rating = totalRating.divide(BigDecimal.valueOf(this.ratingCount), 2, java.math.RoundingMode.HALF_UP);
        }
    }
    
    public void increaseSales(int quantity) {
        this.salesCount += quantity;
    }
    
    public void decreaseStock(int quantity) {
        if (this.stock >= quantity) {
            this.stock -= quantity;
        }
    }
    
    public boolean isAvailable() {
        return enabled && status == MealStatus.AVAILABLE && stock > 0;
    }
    
    public void addTag(String tag) {
        this.tags.add(tag);
    }
    
    public void removeTag(String tag) {
        this.tags.remove(tag);
    }
    
    public void addImage(String imageUrl) {
        this.imageUrls.add(imageUrl);
    }
    
    public void removeImage(String imageUrl) {
        this.imageUrls.remove(imageUrl);
    }
    
    // Getters and Setters
    public Merchant getMerchant() {
        return merchant;
    }
    
    public void setMerchant(Merchant merchant) {
        this.merchant = merchant;
    }
    
    public Category getCategory() {
        return category;
    }
    
    public void setCategory(Category category) {
        this.category = category;
    }
    
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public BigDecimal getPrice() {
        return price;
    }
    
    public void setPrice(BigDecimal price) {
        this.price = price;
    }
    
    public BigDecimal getOriginalPrice() {
        return originalPrice;
    }
    
    public void setOriginalPrice(BigDecimal originalPrice) {
        this.originalPrice = originalPrice;
    }
    
    public String getImageUrl() {
        return imageUrl;
    }
    
    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }
    
    public Set<String> getImageUrls() {
        return imageUrls;
    }
    
    public void setImageUrls(Set<String> imageUrls) {
        this.imageUrls = imageUrls;
    }
    
    public MealStatus getStatus() {
        return status;
    }
    
    public void setStatus(MealStatus status) {
        this.status = status;
    }
    
    public Integer getStock() {
        return stock;
    }
    
    public void setStock(Integer stock) {
        this.stock = stock;
    }
    
    public Integer getSalesCount() {
        return salesCount;
    }
    
    public void setSalesCount(Integer salesCount) {
        this.salesCount = salesCount;
    }
    
    public BigDecimal getRating() {
        return rating;
    }
    
    public void setRating(BigDecimal rating) {
        this.rating = rating;
    }
    
    public Integer getRatingCount() {
        return ratingCount;
    }
    
    public void setRatingCount(Integer ratingCount) {
        this.ratingCount = ratingCount;
    }
    
    public Integer getSortOrder() {
        return sortOrder;
    }
    
    public void setSortOrder(Integer sortOrder) {
        this.sortOrder = sortOrder;
    }
    
    public Set<String> getTags() {
        return tags;
    }
    
    public void setTags(Set<String> tags) {
        this.tags = tags;
    }
    
    public Integer getSpicyLevel() {
        return spicyLevel;
    }
    
    public void setSpicyLevel(Integer spicyLevel) {
        this.spicyLevel = spicyLevel;
    }
    
    public Integer getCalories() {
        return calories;
    }
    
    public void setCalories(Integer calories) {
        this.calories = calories;
    }
    
    public Integer getPreparationTime() {
        return preparationTime;
    }
    
    public void setPreparationTime(Integer preparationTime) {
        this.preparationTime = preparationTime;
    }
    
    public Boolean getEnabled() {
        return enabled;
    }
    
    public void setEnabled(Boolean enabled) {
        this.enabled = enabled;
    }
}
