package com.foodorder.controller;

import com.foodorder.common.response.ApiResponse;
import com.foodorder.common.response.PageResponse;
import com.foodorder.dto.meal.MealDto;
import com.foodorder.dto.meal.MealSearchRequest;
import com.foodorder.service.MealService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.List;

@RestController
@RequestMapping("/meals")
@Tag(name = "餐饮管理", description = "餐饮商品相关接口")
public class MealController {
    
    @Autowired
    private MealService mealService;
    
    @GetMapping("/{id}")
    @Operation(summary = "获取餐饮详情", description = "根据ID获取餐饮商品详细信息")
    public ResponseEntity<ApiResponse<MealDto>> getMealById(@PathVariable Long id) {
        MealDto meal = mealService.getMealById(id);
        return ResponseEntity.ok(ApiResponse.success(meal));
    }
    
    @GetMapping
    @Operation(summary = "获取可用餐饮列表", description = "分页获取所有可用的餐饮商品")
    public ResponseEntity<ApiResponse<PageResponse<MealDto>>> getAvailableMeals(
            @Parameter(description = "页码，从0开始") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "20") int size) {
        
        Pageable pageable = PageRequest.of(page, size);
        Page<MealDto> meals = mealService.getAvailableMeals(pageable);
        return ResponseEntity.ok(ApiResponse.success(PageResponse.of(meals)));
    }
    
    @GetMapping("/category/{categoryId}")
    @Operation(summary = "按分类获取餐饮", description = "根据分类ID获取餐饮商品列表")
    public ResponseEntity<ApiResponse<PageResponse<MealDto>>> getMealsByCategory(
            @PathVariable Long categoryId,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {
        
        Pageable pageable = PageRequest.of(page, size);
        Page<MealDto> meals = mealService.getMealsByCategory(categoryId, pageable);
        return ResponseEntity.ok(ApiResponse.success(PageResponse.of(meals)));
    }
    
    @GetMapping("/merchant/{merchantId}")
    @Operation(summary = "按商家获取餐饮", description = "根据商家ID获取餐饮商品列表")
    public ResponseEntity<ApiResponse<PageResponse<MealDto>>> getMealsByMerchant(
            @PathVariable Long merchantId,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {
        
        Pageable pageable = PageRequest.of(page, size);
        Page<MealDto> meals = mealService.getMealsByMerchant(merchantId, pageable);
        return ResponseEntity.ok(ApiResponse.success(PageResponse.of(meals)));
    }
    
    @PostMapping("/search")
    @Operation(summary = "搜索餐饮", description = "根据条件搜索餐饮商品")
    public ResponseEntity<ApiResponse<PageResponse<MealDto>>> searchMeals(
            @RequestBody MealSearchRequest request,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {
        
        Pageable pageable = PageRequest.of(page, size);
        Page<MealDto> meals = mealService.searchMeals(request, pageable);
        return ResponseEntity.ok(ApiResponse.success(PageResponse.of(meals)));
    }
    
    @GetMapping("/price-range")
    @Operation(summary = "按价格范围获取餐饮", description = "根据价格范围获取餐饮商品")
    public ResponseEntity<ApiResponse<PageResponse<MealDto>>> getMealsByPriceRange(
            @RequestParam BigDecimal minPrice,
            @RequestParam BigDecimal maxPrice,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {
        
        Pageable pageable = PageRequest.of(page, size);
        Page<MealDto> meals = mealService.getMealsByPriceRange(minPrice, maxPrice, pageable);
        return ResponseEntity.ok(ApiResponse.success(PageResponse.of(meals)));
    }
    
    @GetMapping("/spicy/{level}")
    @Operation(summary = "按辣度获取餐饮", description = "根据辣度等级获取餐饮商品")
    public ResponseEntity<ApiResponse<PageResponse<MealDto>>> getMealsBySpicyLevel(
            @PathVariable Integer level,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {
        
        Pageable pageable = PageRequest.of(page, size);
        Page<MealDto> meals = mealService.getMealsBySpicyLevel(level, pageable);
        return ResponseEntity.ok(ApiResponse.success(PageResponse.of(meals)));
    }
    
    @GetMapping("/popular")
    @Operation(summary = "获取热门餐饮", description = "获取销量最高的餐饮商品")
    public ResponseEntity<ApiResponse<List<MealDto>>> getPopularMeals(
            @RequestParam(defaultValue = "10") int limit) {
        
        List<MealDto> meals = mealService.getPopularMeals(limit);
        return ResponseEntity.ok(ApiResponse.success(meals));
    }
    
    @GetMapping("/top-rated")
    @Operation(summary = "获取高评分餐饮", description = "获取评分最高的餐饮商品")
    public ResponseEntity<ApiResponse<List<MealDto>>> getTopRatedMeals(
            @RequestParam(defaultValue = "10") int limit) {
        
        List<MealDto> meals = mealService.getTopRatedMeals(limit);
        return ResponseEntity.ok(ApiResponse.success(meals));
    }
}
