# 订餐系统启动指南

## 环境要求

### 必需环境
- **Java**: JDK 8 或更高版本
- **Maven**: 3.6+ （用于构建项目）
- **IDE**: IntelliJ IDEA 或 Eclipse（推荐）

### 可选环境
- **MySQL**: 8.0+（生产环境数据库）
- **Redis**: 6.0+（缓存服务）

## 快速启动

### 1. 环境检查
```bash
# 检查Java版本
java -version

# 检查Maven版本
mvn -version
```

### 2. 项目构建
```bash
# 进入项目目录
cd food-order-system

# 清理并编译项目
mvn clean compile

# 运行测试
mvn test

# 打包项目
mvn package
```

### 3. 启动应用

#### 方式一：使用Maven启动
```bash
# 使用默认配置启动（H2内存数据库）
mvn spring-boot:run

# 使用开发环境配置启动
mvn spring-boot:run -Dspring-boot.run.profiles=dev
```

#### 方式二：使用JAR包启动
```bash
# 打包后启动
java -jar target/food-order-system-0.0.1-SNAPSHOT.jar

# 指定配置文件启动
java -jar target/food-order-system-0.0.1-SNAPSHOT.jar --spring.profiles.active=dev
```

#### 方式三：IDE启动
1. 导入项目到IDE
2. 运行 `FoodOrderSystemApplication.java` 主类

### 4. 访问应用

启动成功后，可以通过以下地址访问：

- **应用主页**: http://localhost:8080/api
- **API文档**: http://localhost:8080/api/swagger-ui.html
- **H2数据库控制台**: http://localhost:8080/api/h2-console
- **健康检查**: http://localhost:8080/api/actuator/health

## 配置说明

### 默认配置（application.yml）
- 使用H2内存数据库
- 端口：8080
- 上下文路径：/api
- 自动创建数据库表
- 启用H2控制台

### 开发环境配置（application-dev.yml）
- 使用MySQL数据库
- 需要配置数据库连接信息
- 更新数据库表结构（不删除数据）

### 生产环境配置（application-prod.yml）
- 使用环境变量配置
- 验证数据库表结构
- 优化的连接池配置

## 数据库配置

### H2内存数据库（默认）
无需额外配置，应用启动时自动创建。

**H2控制台访问：**
- URL: http://localhost:8080/api/h2-console
- JDBC URL: jdbc:h2:mem:testdb
- 用户名: sa
- 密码: password

### MySQL数据库（开发/生产环境）

1. **创建数据库**
```sql
CREATE DATABASE food_order_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

2. **配置连接信息**
```yaml
spring:
  datasource:
    url: *********************************************
    username: your_username
    password: your_password
```

3. **启动应用**
```bash
mvn spring-boot:run -Dspring-boot.run.profiles=dev
```

## Redis配置（可选）

### 安装Redis
```bash
# Windows (使用Chocolatey)
choco install redis-64

# macOS (使用Homebrew)
brew install redis

# Ubuntu/Debian
sudo apt-get install redis-server
```

### 启动Redis
```bash
# 启动Redis服务
redis-server

# 检查Redis状态
redis-cli ping
```

### 配置Redis连接
```yaml
spring:
  data:
    redis:
      host: localhost
      port: 6379
      password: # 如果设置了密码
```

## 初始数据

应用启动时会自动导入初始数据：

### 默认用户
- **管理员**: admin / password
- **商家1**: merchant1 / password  
- **商家2**: merchant2 / password
- **客户1**: customer1 / password
- **客户2**: customer2 / password

### 默认数据
- 餐饮分类（中式、西式、日韩料理等）
- 商家信息（川味小厨、意式风情）
- 餐饮商品（麻婆豆腐、宫保鸡丁等）
- 用户评分数据（从JSON文件导入）

## API测试

### 使用Swagger UI
访问 http://localhost:8080/api/swagger-ui.html 进行API测试

### 使用curl命令

#### 1. 用户注册
```bash
curl -X POST http://localhost:8080/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "username": "testuser",
    "password": "password123",
    "confirmPassword": "password123",
    "email": "<EMAIL>",
    "realName": "测试用户"
  }'
```

#### 2. 用户登录
```bash
curl -X POST http://localhost:8080/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "usernameOrEmail": "testuser",
    "password": "password123"
  }'
```

#### 3. 获取餐饮列表
```bash
curl -X GET http://localhost:8080/api/meals
```

#### 4. 创建订单（需要JWT token）
```bash
curl -X POST http://localhost:8080/api/orders \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "merchantId": 1,
    "orderItems": [
      {
        "mealId": 1,
        "quantity": 2
      }
    ],
    "deliveryAddress": "测试地址",
    "deliveryPhone": "13800138000"
  }'
```

## 常见问题

### 1. 端口冲突
如果8080端口被占用，可以修改配置：
```yaml
server:
  port: 8081
```

### 2. 数据库连接失败
- 检查数据库服务是否启动
- 验证连接信息是否正确
- 确认数据库用户权限

### 3. Redis连接失败
- 检查Redis服务是否启动
- 验证Redis配置信息
- 可以暂时禁用Redis相关功能

### 4. 内存不足
增加JVM内存：
```bash
java -Xmx1024m -jar target/food-order-system-0.0.1-SNAPSHOT.jar
```

## 开发建议

### 1. IDE配置
- 安装Lombok插件
- 配置代码格式化规则
- 启用自动导入优化

### 2. 调试技巧
- 使用IDE断点调试
- 查看应用日志文件
- 使用Actuator监控端点

### 3. 数据库管理
- 使用数据库管理工具（如DBeaver）
- 定期备份重要数据
- 监控数据库性能

## 下一步

1. 熟悉API接口文档
2. 了解数据库表结构
3. 学习业务流程
4. 参与功能开发
5. 编写测试用例

如有问题，请查看项目文档或联系开发团队。
