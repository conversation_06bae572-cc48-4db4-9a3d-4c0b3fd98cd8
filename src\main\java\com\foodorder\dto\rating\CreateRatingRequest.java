package com.foodorder.dto.rating;

import jakarta.validation.constraints.DecimalMax;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.NotNull;

import java.math.BigDecimal;

public class CreateRatingRequest {
    
    @NotNull(message = "商品ID不能为空")
    private Long mealId;
    
    private Long orderId;
    
    @NotNull(message = "评分不能为空")
    @DecimalMin(value = "1.0", message = "评分不能低于1分")
    @DecimalMax(value = "5.0", message = "评分不能高于5分")
    private BigDecimal score;
    
    private String review;
    
    private Boolean anonymous = false;
    
    public CreateRatingRequest() {}
    
    public CreateRatingRequest(Long mealId, BigDecimal score) {
        this.mealId = mealId;
        this.score = score;
    }
    
    // Getters and Setters
    public Long getMealId() {
        return mealId;
    }
    
    public void setMealId(Long mealId) {
        this.mealId = mealId;
    }
    
    public Long getOrderId() {
        return orderId;
    }
    
    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }
    
    public BigDecimal getScore() {
        return score;
    }
    
    public void setScore(BigDecimal score) {
        this.score = score;
    }
    
    public String getReview() {
        return review;
    }
    
    public void setReview(String review) {
        this.review = review;
    }
    
    public Boolean getAnonymous() {
        return anonymous;
    }
    
    public void setAnonymous(Boolean anonymous) {
        this.anonymous = anonymous;
    }
}
