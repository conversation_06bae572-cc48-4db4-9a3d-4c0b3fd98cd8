package com.foodorder.repository;

import com.foodorder.entity.Meal;
import com.foodorder.entity.Order;
import com.foodorder.entity.OrderItem;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface OrderItemRepository extends JpaRepository<OrderItem, Long> {
    
    List<OrderItem> findByOrder(Order order);
    
    List<OrderItem> findByMeal(Meal meal);
    
    @Query("SELECT oi FROM OrderItem oi WHERE oi.order.id = :orderId")
    List<OrderItem> findByOrderId(@Param("orderId") Long orderId);
    
    @Query("SELECT oi FROM OrderItem oi WHERE oi.meal.id = :mealId")
    List<OrderItem> findByMealId(@Param("mealId") Long mealId);
    
    @Query("SELECT SUM(oi.quantity) FROM OrderItem oi WHERE oi.meal = :meal")
    Long sumQuantityByMeal(@Param("meal") Meal meal);
    
    @Query("SELECT oi.meal, SUM(oi.quantity) as totalQuantity FROM OrderItem oi " +
           "WHERE oi.order.status = 'DELIVERED' " +
           "GROUP BY oi.meal ORDER BY totalQuantity DESC")
    List<Object[]> findMostOrderedMeals();
}
