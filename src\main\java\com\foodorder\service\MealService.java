package com.foodorder.service;

import com.foodorder.common.exception.ResourceNotFoundException;
import com.foodorder.dto.meal.MealDto;
import com.foodorder.dto.meal.MealSearchRequest;
import com.foodorder.entity.Category;
import com.foodorder.entity.Meal;
import com.foodorder.entity.Merchant;
import com.foodorder.repository.CategoryRepository;
import com.foodorder.repository.MealRepository;
import com.foodorder.repository.MerchantRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class MealService {
    
    private static final Logger logger = LoggerFactory.getLogger(MealService.class);
    
    @Autowired
    private MealRepository mealRepository;
    
    @Autowired
    private CategoryRepository categoryRepository;
    
    @Autowired
    private MerchantRepository merchantRepository;
    
    public MealDto getMealById(Long id) {
        Meal meal = mealRepository.findById(id)
            .orElseThrow(() -> new ResourceNotFoundException("商品不存在"));
        return MealDto.fromMeal(meal);
    }
    
    @Cacheable(value = "meals", key = "'available'")
    public Page<MealDto> getAvailableMeals(Pageable pageable) {
        Page<Meal> meals = mealRepository.findAvailableMeals(pageable);
        return meals.map(MealDto::fromMeal);
    }
    
    public Page<MealDto> getMealsByCategory(Long categoryId, Pageable pageable) {
        Category category = categoryRepository.findById(categoryId)
            .orElseThrow(() -> new ResourceNotFoundException("分类不存在"));
        
        Page<Meal> meals = mealRepository.findAvailableMealsByCategory(category, pageable);
        return meals.map(MealDto::fromMeal);
    }
    
    public Page<MealDto> getMealsByMerchant(Long merchantId, Pageable pageable) {
        Merchant merchant = merchantRepository.findById(merchantId)
            .orElseThrow(() -> new ResourceNotFoundException("商家不存在"));
        
        Page<Meal> meals = mealRepository.findAvailableMealsByMerchant(merchant, pageable);
        return meals.map(MealDto::fromMeal);
    }
    
    public Page<MealDto> searchMeals(MealSearchRequest request, Pageable pageable) {
        // 构建排序
        Sort sort = buildSort(request.getSortBy(), request.getSortDirection());
        Pageable sortedPageable = PageRequest.of(pageable.getPageNumber(), pageable.getPageSize(), sort);
        
        Page<Meal> meals;
        
        // 如果有关键词搜索
        if (StringUtils.hasText(request.getKeyword())) {
            meals = mealRepository.searchAvailableMeals(request.getKeyword(), sortedPageable);
        } else {
            meals = mealRepository.findAvailableMeals(sortedPageable);
        }
        
        // 应用其他过滤条件
        return meals.map(MealDto::fromMeal);
    }
    
    public Page<MealDto> getMealsByPriceRange(BigDecimal minPrice, BigDecimal maxPrice, Pageable pageable) {
        Page<Meal> meals = mealRepository.findMealsByPriceRange(minPrice, maxPrice, pageable);
        return meals.map(MealDto::fromMeal);
    }
    
    public Page<MealDto> getMealsBySpicyLevel(Integer spicyLevel, Pageable pageable) {
        Page<Meal> meals = mealRepository.findMealsBySpicyLevel(spicyLevel, pageable);
        return meals.map(MealDto::fromMeal);
    }
    
    public Page<MealDto> getMealsByTags(List<String> tags, Pageable pageable) {
        Page<Meal> meals = mealRepository.findMealsByTags(tags, pageable);
        return meals.map(MealDto::fromMeal);
    }
    
    @Cacheable(value = "popularMeals", key = "'top' + #limit")
    public List<MealDto> getPopularMeals(int limit) {
        Pageable pageable = PageRequest.of(0, limit);
        List<Meal> meals = mealRepository.findPopularMeals(pageable);
        return meals.stream()
            .map(MealDto::fromMeal)
            .collect(Collectors.toList());
    }
    
    @Cacheable(value = "topRatedMeals", key = "'top' + #limit")
    public List<MealDto> getTopRatedMeals(int limit) {
        Pageable pageable = PageRequest.of(0, limit);
        List<Meal> meals = mealRepository.findTopRatedMeals(pageable);
        return meals.stream()
            .map(MealDto::fromMeal)
            .collect(Collectors.toList());
    }
    
    private Sort buildSort(String sortBy, String sortDirection) {
        Sort.Direction direction = "desc".equalsIgnoreCase(sortDirection) ? 
            Sort.Direction.DESC : Sort.Direction.ASC;
        
        switch (sortBy) {
            case "price":
                return Sort.by(direction, "price");
            case "rating":
                return Sort.by(direction, "rating");
            case "salesCount":
                return Sort.by(direction, "salesCount");
            case "createdAt":
                return Sort.by(direction, "createdAt");
            default:
                return Sort.by(direction, "id");
        }
    }
    
    public boolean isMealAvailable(Long mealId) {
        return mealRepository.findById(mealId)
            .map(Meal::isAvailable)
            .orElse(false);
    }
    
    public boolean hasSufficientStock(Long mealId, int quantity) {
        return mealRepository.findById(mealId)
            .map(meal -> meal.getStock() >= quantity)
            .orElse(false);
    }
}
