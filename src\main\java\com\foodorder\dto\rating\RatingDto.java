package com.foodorder.dto.rating;

import com.foodorder.entity.Rating;

import java.math.BigDecimal;
import java.time.LocalDateTime;

public class RatingDto {
    
    private Long id;
    private Long userId;
    private String username;
    private Long mealId;
    private String mealName;
    private Long merchantId;
    private String merchantName;
    private Long orderId;
    private String orderNumber;
    private BigDecimal score;
    private String review;
    private Boolean anonymous;
    private Integer helpfulCount;
    private LocalDateTime createdAt;
    
    public RatingDto() {}
    
    public RatingDto(Rating rating) {
        this.id = rating.getId();
        this.userId = rating.getUser().getId();
        this.username = rating.getAnonymous() ? "匿名用户" : 
            (rating.getUser().getRealName() != null ? rating.getUser().getRealName() : rating.getUser().getUsername());
        this.mealId = rating.getMeal().getId();
        this.mealName = rating.getMeal().getName();
        this.merchantId = rating.getMerchant().getId();
        this.merchantName = rating.getMerchant().getName();
        
        if (rating.getOrder() != null) {
            this.orderId = rating.getOrder().getId();
            this.orderNumber = rating.getOrder().getOrderNumber();
        }
        
        this.score = rating.getScore();
        this.review = rating.getReview();
        this.anonymous = rating.getAnonymous();
        this.helpfulCount = rating.getHelpfulCount();
        this.createdAt = rating.getCreatedAt();
    }
    
    public static RatingDto fromRating(Rating rating) {
        return new RatingDto(rating);
    }
    
    // Getters and Setters
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public Long getUserId() {
        return userId;
    }
    
    public void setUserId(Long userId) {
        this.userId = userId;
    }
    
    public String getUsername() {
        return username;
    }
    
    public void setUsername(String username) {
        this.username = username;
    }
    
    public Long getMealId() {
        return mealId;
    }
    
    public void setMealId(Long mealId) {
        this.mealId = mealId;
    }
    
    public String getMealName() {
        return mealName;
    }
    
    public void setMealName(String mealName) {
        this.mealName = mealName;
    }
    
    public Long getMerchantId() {
        return merchantId;
    }
    
    public void setMerchantId(Long merchantId) {
        this.merchantId = merchantId;
    }
    
    public String getMerchantName() {
        return merchantName;
    }
    
    public void setMerchantName(String merchantName) {
        this.merchantName = merchantName;
    }
    
    public Long getOrderId() {
        return orderId;
    }
    
    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }
    
    public String getOrderNumber() {
        return orderNumber;
    }
    
    public void setOrderNumber(String orderNumber) {
        this.orderNumber = orderNumber;
    }
    
    public BigDecimal getScore() {
        return score;
    }
    
    public void setScore(BigDecimal score) {
        this.score = score;
    }
    
    public String getReview() {
        return review;
    }
    
    public void setReview(String review) {
        this.review = review;
    }
    
    public Boolean getAnonymous() {
        return anonymous;
    }
    
    public void setAnonymous(Boolean anonymous) {
        this.anonymous = anonymous;
    }
    
    public Integer getHelpfulCount() {
        return helpfulCount;
    }
    
    public void setHelpfulCount(Integer helpfulCount) {
        this.helpfulCount = helpfulCount;
    }
    
    public LocalDateTime getCreatedAt() {
        return createdAt;
    }
    
    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }
}
