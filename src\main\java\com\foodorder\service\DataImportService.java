package com.foodorder.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.foodorder.entity.*;
import com.foodorder.repository.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;

@Service
public class DataImportService {
    
    private static final Logger logger = LoggerFactory.getLogger(DataImportService.class);
    
    @Autowired
    private UserRepository userRepository;
    
    @Autowired
    private MerchantRepository merchantRepository;
    
    @Autowired
    private CategoryRepository categoryRepository;
    
    @Autowired
    private MealRepository mealRepository;
    
    @Autowired
    private RatingRepository ratingRepository;
    
    private final ObjectMapper objectMapper = new ObjectMapper();
    
    @Transactional
    public void importRatingData() {
        logger.info("开始导入评分数据...");
        
        try {
            ClassPathResource resource = new ClassPathResource("../user_meal_rating.json");
            BufferedReader reader = new BufferedReader(new InputStreamReader(resource.getInputStream()));
            
            String line;
            int count = 0;
            int batchSize = 100;
            List<Rating> ratings = new ArrayList<>();
            
            // 获取现有的用户、商家、分类和商品
            Map<String, User> userMap = createUsersFromRatingData(reader);
            Map<String, Merchant> merchantMap = createMerchantsForUsers(userMap);
            Map<String, Meal> mealMap = createMealsFromRatingData();
            
            // 重新读取文件处理评分数据
            reader = new BufferedReader(new InputStreamReader(resource.getInputStream()));
            
            while ((line = reader.readLine()) != null && count < 1000) { // 限制导入数量
                try {
                    JsonNode node = objectMapper.readTree(line);
                    
                    String userId = node.get("UserID").asText();
                    String mealId = node.get("MealID").asText();
                    double ratingValue = node.get("Rating").asDouble();
                    String review = node.has("Review") ? node.get("Review").asText() : null;
                    long reviewTime = node.get("ReviewTime").asLong();
                    
                    User user = userMap.get(userId);
                    Meal meal = mealMap.get(mealId);
                    
                    if (user != null && meal != null) {
                        Rating rating = new Rating();
                        rating.setUser(user);
                        rating.setMeal(meal);
                        rating.setMerchant(meal.getMerchant());
                        rating.setScore(BigDecimal.valueOf(ratingValue));
                        rating.setReview(review);
                        rating.setCreatedAt(LocalDateTime.ofInstant(
                            Instant.ofEpochSecond(reviewTime), ZoneId.systemDefault()));
                        
                        ratings.add(rating);
                        count++;
                        
                        if (ratings.size() >= batchSize) {
                            ratingRepository.saveAll(ratings);
                            ratings.clear();
                            logger.info("已导入 {} 条评分数据", count);
                        }
                    }
                } catch (Exception e) {
                    logger.warn("解析评分数据失败: {}", line, e);
                }
            }
            
            // 保存剩余的数据
            if (!ratings.isEmpty()) {
                ratingRepository.saveAll(ratings);
            }
            
            reader.close();
            logger.info("评分数据导入完成，共导入 {} 条记录", count);
            
            // 更新商品和商家的评分统计
            updateRatingStatistics();
            
        } catch (IOException e) {
            logger.error("导入评分数据失败", e);
        }
    }
    
    private Map<String, User> createUsersFromRatingData(BufferedReader reader) throws IOException {
        Map<String, User> userMap = new HashMap<>();
        Set<String> userIds = new HashSet<>();
        
        String line;
        while ((line = reader.readLine()) != null) {
            try {
                JsonNode node = objectMapper.readTree(line);
                String userId = node.get("UserID").asText();
                userIds.add(userId);
            } catch (Exception e) {
                logger.warn("解析用户ID失败: {}", line);
            }
        }
        
        // 创建用户
        int userIndex = 1000; // 从1000开始，避免与初始数据冲突
        for (String userId : userIds) {
            if (userIndex > 1100) break; // 限制用户数量
            
            User user = new User();
            user.setUsername("user_" + userId.substring(0, Math.min(8, userId.length())));
            user.setPassword("$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9lBaLzfzUOoqIQi"); // 默认密码
            user.setEmail("user_" + userIndex + "@example.com");
            user.setRealName("用户" + userIndex);
            user.getRoles().add(UserRole.CUSTOMER);
            
            user = userRepository.save(user);
            userMap.put(userId, user);
            userIndex++;
        }
        
        logger.info("创建了 {} 个用户", userMap.size());
        return userMap;
    }
    
    private Map<String, Merchant> createMerchantsForUsers(Map<String, User> userMap) {
        Map<String, Merchant> merchantMap = new HashMap<>();
        
        // 为部分用户创建商家身份
        int merchantCount = 0;
        for (Map.Entry<String, User> entry : userMap.entrySet()) {
            if (merchantCount >= 10) break; // 限制商家数量
            
            User user = entry.getValue();
            user.getRoles().add(UserRole.MERCHANT);
            userRepository.save(user);
            
            Merchant merchant = new Merchant();
            merchant.setUser(user);
            merchant.setName("餐厅" + (merchantCount + 1));
            merchant.setDescription("美味餐厅，提供优质服务");
            merchant.setAddress("测试地址" + (merchantCount + 1));
            merchant.setPhone("1380013800" + merchantCount);
            merchant.setStatus(MerchantStatus.APPROVED);
            merchant.setOpeningHours("09:00-22:00");
            merchant.setDeliveryFee(BigDecimal.valueOf(5.0));
            merchant.setMinOrderAmount(BigDecimal.valueOf(20.0));
            
            merchant = merchantRepository.save(merchant);
            merchantMap.put(entry.getKey(), merchant);
            merchantCount++;
        }
        
        logger.info("创建了 {} 个商家", merchantMap.size());
        return merchantMap;
    }
    
    private Map<String, Meal> createMealsFromRatingData() throws IOException {
        Map<String, Meal> mealMap = new HashMap<>();
        Set<String> mealIds = new HashSet<>();
        
        // 读取所有商品ID
        ClassPathResource resource = new ClassPathResource("../user_meal_rating.json");
        BufferedReader reader = new BufferedReader(new InputStreamReader(resource.getInputStream()));
        
        String line;
        while ((line = reader.readLine()) != null) {
            try {
                JsonNode node = objectMapper.readTree(line);
                String mealId = node.get("MealID").asText();
                mealIds.add(mealId);
            } catch (Exception e) {
                logger.warn("解析商品ID失败: {}", line);
            }
        }
        reader.close();
        
        // 获取商家和分类
        List<Merchant> merchants = merchantRepository.findAll();
        List<Category> categories = categoryRepository.findByParentIsNotNull(); // 获取子分类
        
        if (merchants.isEmpty() || categories.isEmpty()) {
            logger.warn("没有找到商家或分类数据，无法创建商品");
            return mealMap;
        }
        
        // 创建商品
        int mealIndex = 0;
        Random random = new Random();
        
        for (String mealId : mealIds) {
            if (mealIndex >= 50) break; // 限制商品数量
            
            Merchant merchant = merchants.get(random.nextInt(merchants.size()));
            Category category = categories.get(random.nextInt(categories.size()));
            
            Meal meal = new Meal();
            meal.setMerchant(merchant);
            meal.setCategory(category);
            meal.setName("商品_" + mealId.substring(0, Math.min(8, mealId.length())));
            meal.setDescription("美味的" + category.getName());
            meal.setPrice(BigDecimal.valueOf(20 + random.nextInt(80))); // 20-100元随机价格
            meal.setStock(50 + random.nextInt(100));
            meal.setSpicyLevel(random.nextInt(4));
            meal.setPreparationTime(10 + random.nextInt(30));
            meal.getTags().add("美味");
            meal.getTags().add("推荐");
            
            meal = mealRepository.save(meal);
            mealMap.put(mealId, meal);
            mealIndex++;
        }
        
        logger.info("创建了 {} 个商品", mealMap.size());
        return mealMap;
    }
    
    private void updateRatingStatistics() {
        logger.info("开始更新评分统计...");
        
        // 更新商品评分统计
        List<Meal> meals = mealRepository.findAll();
        for (Meal meal : meals) {
            List<Rating> ratings = ratingRepository.findByMeal(meal);
            if (!ratings.isEmpty()) {
                BigDecimal totalRating = ratings.stream()
                    .map(Rating::getScore)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
                
                meal.setRating(totalRating.divide(BigDecimal.valueOf(ratings.size()), 2, java.math.RoundingMode.HALF_UP));
                meal.setRatingCount(ratings.size());
                mealRepository.save(meal);
            }
        }
        
        // 更新商家评分统计
        List<Merchant> merchants = merchantRepository.findAll();
        for (Merchant merchant : merchants) {
            List<Rating> ratings = ratingRepository.findByMerchant(merchant);
            if (!ratings.isEmpty()) {
                BigDecimal totalRating = ratings.stream()
                    .map(Rating::getScore)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
                
                merchant.setRating(totalRating.divide(BigDecimal.valueOf(ratings.size()), 2, java.math.RoundingMode.HALF_UP));
                merchant.setRatingCount(ratings.size());
                merchantRepository.save(merchant);
            }
        }
        
        logger.info("评分统计更新完成");
    }
}
