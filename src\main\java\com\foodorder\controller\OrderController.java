package com.foodorder.controller;

import com.foodorder.common.response.ApiResponse;
import com.foodorder.common.response.PageResponse;
import com.foodorder.dto.order.CreateOrderRequest;
import com.foodorder.dto.order.OrderDto;
import com.foodorder.entity.OrderStatus;
import com.foodorder.service.OrderService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/orders")
@Tag(name = "订单管理", description = "订单相关接口")
public class OrderController {
    
    @Autowired
    private OrderService orderService;
    
    @PostMapping
    @PreAuthorize("hasRole('CUSTOMER')")
    @Operation(summary = "创建订单", description = "客户创建新订单")
    public ResponseEntity<ApiResponse<OrderDto>> createOrder(@Valid @RequestBody CreateOrderRequest request) {
        OrderDto order = orderService.createOrder(request);
        return ResponseEntity.ok(ApiResponse.success("订单创建成功", order));
    }
    
    @GetMapping("/{id}")
    @PreAuthorize("hasRole('CUSTOMER') or hasRole('MERCHANT') or hasRole('ADMIN')")
    @Operation(summary = "获取订单详情", description = "根据ID获取订单详细信息")
    public ResponseEntity<ApiResponse<OrderDto>> getOrderById(@PathVariable Long id) {
        OrderDto order = orderService.getOrderById(id);
        return ResponseEntity.ok(ApiResponse.success(order));
    }
    
    @GetMapping("/number/{orderNumber}")
    @PreAuthorize("hasRole('CUSTOMER') or hasRole('MERCHANT') or hasRole('ADMIN')")
    @Operation(summary = "根据订单号获取订单", description = "根据订单号获取订单详细信息")
    public ResponseEntity<ApiResponse<OrderDto>> getOrderByNumber(@PathVariable String orderNumber) {
        OrderDto order = orderService.getOrderByNumber(orderNumber);
        return ResponseEntity.ok(ApiResponse.success(order));
    }
    
    @GetMapping("/my")
    @PreAuthorize("hasRole('CUSTOMER')")
    @Operation(summary = "获取我的订单", description = "获取当前用户的订单列表")
    public ResponseEntity<ApiResponse<PageResponse<OrderDto>>> getMyOrders(
            @Parameter(description = "页码，从0开始") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "20") int size) {
        
        Pageable pageable = PageRequest.of(page, size);
        Page<OrderDto> orders = orderService.getCustomerOrders(pageable);
        return ResponseEntity.ok(ApiResponse.success(PageResponse.of(orders)));
    }
    
    @GetMapping("/my/status/{status}")
    @PreAuthorize("hasRole('CUSTOMER')")
    @Operation(summary = "按状态获取我的订单", description = "根据订单状态获取当前用户的订单列表")
    public ResponseEntity<ApiResponse<PageResponse<OrderDto>>> getMyOrdersByStatus(
            @PathVariable OrderStatus status,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {
        
        Pageable pageable = PageRequest.of(page, size);
        Page<OrderDto> orders = orderService.getCustomerOrdersByStatus(status, pageable);
        return ResponseEntity.ok(ApiResponse.success(PageResponse.of(orders)));
    }
    
    @PostMapping("/{id}/cancel")
    @PreAuthorize("hasRole('CUSTOMER')")
    @Operation(summary = "取消订单", description = "客户取消订单")
    public ResponseEntity<ApiResponse<Void>> cancelOrder(
            @PathVariable Long id,
            @RequestParam(required = false, defaultValue = "用户主动取消") String reason) {
        
        orderService.cancelOrder(id, reason);
        return ResponseEntity.ok(ApiResponse.success("订单取消成功"));
    }
}
