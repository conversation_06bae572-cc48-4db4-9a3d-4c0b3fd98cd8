# 食品订购系统 - 简化版本

## 项目状态
✅ **项目已成功运行！**

这是一个简化版本的Spring Boot应用程序，已经解决了所有编码和编译问题。

## 快速启动

### 1. 编译项目
```bash
.\mvnw.cmd clean compile
```

### 2. 运行应用程序
```bash
.\mvnw.cmd spring-boot:run
```

### 3. 访问应用程序
- 主页: http://localhost:8080
- 健康检查: http://localhost:8080/health  
- API测试: http://localhost:8080/api/test

## 项目结构
```
├── src/
│   └── main/
│       ├── java/
│       │   └── com/foodorder/
│       │       └── Application.java          # 主应用类
│       └── resources/
│           └── application.properties        # 配置文件
├── pom.xml                                   # Maven配置
└── mvnw.cmd                                  # Maven Wrapper
```

## 技术栈
- Spring Boot 2.7.18
- Java 8+
- Maven
- 内嵌Tomcat服务器

## 可用端点
- `GET /` - 欢迎页面
- `GET /health` - 系统健康检查
- `GET /api/test` - API测试端点

## 下一步扩展
这个简化版本为您提供了一个稳定的基础。您可以在此基础上逐步添加：
1. 数据库集成 (H2/MySQL)
2. 用户认证和授权
3. RESTful API
4. 前端界面
5. 推荐系统功能

## 故障排除
如果遇到问题：
1. 确保Java 8+已安装
2. 确保端口8080未被占用
3. 检查防火墙设置
4. 查看控制台日志输出

## 原始项目
原始的复杂项目文件已备份到 `src-old/` 和 `pom-old.xml`，您可以参考这些文件来逐步迁移功能。
