package com.foodorder.common.constants;

public class Constants {
    
    // 用户角色
    public static final String ROLE_CUSTOMER = "CUSTOMER";
    public static final String ROLE_MERCHANT = "MERCHANT";
    public static final String ROLE_ADMIN = "ADMIN";
    
    // JWT相关
    public static final String JWT_HEADER = "Authorization";
    public static final String JWT_PREFIX = "Bearer ";
    
    // 缓存键前缀
    public static final String CACHE_USER_PREFIX = "user:";
    public static final String CACHE_MEAL_PREFIX = "meal:";
    public static final String CACHE_RECOMMENDATION_PREFIX = "recommendation:";
    public static final String CACHE_POPULAR_MEALS = "popular_meals";
    public static final String CACHE_HOT_SEARCH = "hot_search";
    
    // 推荐系统相关
    public static final int DEFAULT_RECOMMENDATION_SIZE = 10;
    public static final int MIN_RATINGS_FOR_RECOMMENDATION = 5;
    public static final int RECENT_RATINGS_COUNT = 10;
    
    // 订单状态
    public static final String ORDER_STATUS_PENDING = "PENDING";
    public static final String ORDER_STATUS_CONFIRMED = "CONFIRMED";
    public static final String ORDER_STATUS_PREPARING = "PREPARING";
    public static final String ORDER_STATUS_READY = "READY";
    public static final String ORDER_STATUS_DELIVERED = "DELIVERED";
    public static final String ORDER_STATUS_CANCELLED = "CANCELLED";
    
    // 商家状态
    public static final String MERCHANT_STATUS_PENDING = "PENDING";
    public static final String MERCHANT_STATUS_APPROVED = "APPROVED";
    public static final String MERCHANT_STATUS_REJECTED = "REJECTED";
    public static final String MERCHANT_STATUS_SUSPENDED = "SUSPENDED";
    
    // 文件上传相关
    public static final String UPLOAD_DIR = "uploads/";
    public static final String MEAL_IMAGE_DIR = "meals/";
    public static final String MERCHANT_IMAGE_DIR = "merchants/";
    public static final long MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB
    
    // 分页相关
    public static final int DEFAULT_PAGE_SIZE = 20;
    public static final int MAX_PAGE_SIZE = 100;
    
    // 评分相关
    public static final double MIN_RATING = 1.0;
    public static final double MAX_RATING = 5.0;
    
    private Constants() {
        // 私有构造函数，防止实例化
    }
}
