package com.foodorder.dto.order;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;

import java.util.List;

public class CreateOrderRequest {
    
    @NotNull(message = "商家ID不能为空")
    private Long merchantId;
    
    @NotEmpty(message = "订单项不能为空")
    @Valid
    private List<CreateOrderItemRequest> orderItems;
    
    @NotBlank(message = "配送地址不能为空")
    private String deliveryAddress;
    
    private String deliveryPhone;
    
    private String deliveryContact;
    
    private String notes;
    
    private String paymentMethod = "ONLINE"; // ONLINE, CASH
    
    public CreateOrderRequest() {}
    
    // Getters and Setters
    public Long getMerchantId() {
        return merchantId;
    }
    
    public void setMerchantId(Long merchantId) {
        this.merchantId = merchantId;
    }
    
    public List<CreateOrderItemRequest> getOrderItems() {
        return orderItems;
    }
    
    public void setOrderItems(List<CreateOrderItemRequest> orderItems) {
        this.orderItems = orderItems;
    }
    
    public String getDeliveryAddress() {
        return deliveryAddress;
    }
    
    public void setDeliveryAddress(String deliveryAddress) {
        this.deliveryAddress = deliveryAddress;
    }
    
    public String getDeliveryPhone() {
        return deliveryPhone;
    }
    
    public void setDeliveryPhone(String deliveryPhone) {
        this.deliveryPhone = deliveryPhone;
    }
    
    public String getDeliveryContact() {
        return deliveryContact;
    }
    
    public void setDeliveryContact(String deliveryContact) {
        this.deliveryContact = deliveryContact;
    }
    
    public String getNotes() {
        return notes;
    }
    
    public void setNotes(String notes) {
        this.notes = notes;
    }
    
    public String getPaymentMethod() {
        return paymentMethod;
    }
    
    public void setPaymentMethod(String paymentMethod) {
        this.paymentMethod = paymentMethod;
    }
}
