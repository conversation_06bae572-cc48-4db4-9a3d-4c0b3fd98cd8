package com.foodorder.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mail.SimpleMailMessage;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

@Service
public class EmailService {
    
    private static final Logger logger = LoggerFactory.getLogger(EmailService.class);
    
    @Autowired
    private JavaMailSender mailSender;
    
    @Value("${spring.mail.username}")
    private String fromEmail;
    
    @Async
    public void sendWelcomeEmail(String toEmail, String username) {
        try {
            SimpleMailMessage message = new SimpleMailMessage();
            message.setFrom(fromEmail);
            message.setTo(toEmail);
            message.setSubject("欢迎注册订餐系统");
            message.setText(String.format(
                "亲爱的 %s，\n\n" +
                "欢迎注册我们的订餐系统！\n\n" +
                "您现在可以：\n" +
                "- 浏览各种美食\n" +
                "- 下单购买\n" +
                "- 评价商品\n" +
                "- 获得个性化推荐\n\n" +
                "感谢您的注册！\n\n" +
                "订餐系统团队",
                username
            ));
            
            mailSender.send(message);
            logger.info("欢迎邮件发送成功: {}", toEmail);
        } catch (Exception e) {
            logger.error("发送欢迎邮件失败: {}", toEmail, e);
        }
    }
    
    @Async
    public void sendPasswordResetEmail(String toEmail, String username, String resetToken) {
        try {
            SimpleMailMessage message = new SimpleMailMessage();
            message.setFrom(fromEmail);
            message.setTo(toEmail);
            message.setSubject("密码重置请求");
            message.setText(String.format(
                "亲爱的 %s，\n\n" +
                "您请求重置密码。请使用以下令牌重置您的密码：\n\n" +
                "重置令牌：%s\n\n" +
                "此令牌将在24小时后过期。\n\n" +
                "如果您没有请求重置密码，请忽略此邮件。\n\n" +
                "订餐系统团队",
                username, resetToken
            ));
            
            mailSender.send(message);
            logger.info("密码重置邮件发送成功: {}", toEmail);
        } catch (Exception e) {
            logger.error("发送密码重置邮件失败: {}", toEmail, e);
        }
    }
    
    @Async
    public void sendOrderConfirmationEmail(String toEmail, String username, String orderNumber) {
        try {
            SimpleMailMessage message = new SimpleMailMessage();
            message.setFrom(fromEmail);
            message.setTo(toEmail);
            message.setSubject("订单确认通知");
            message.setText(String.format(
                "亲爱的 %s，\n\n" +
                "您的订单已确认！\n\n" +
                "订单号：%s\n\n" +
                "我们正在为您准备美食，请耐心等待。\n\n" +
                "订餐系统团队",
                username, orderNumber
            ));
            
            mailSender.send(message);
            logger.info("订单确认邮件发送成功: {}", toEmail);
        } catch (Exception e) {
            logger.error("发送订单确认邮件失败: {}", toEmail, e);
        }
    }
    
    @Async
    public void sendOrderDeliveredEmail(String toEmail, String username, String orderNumber) {
        try {
            SimpleMailMessage message = new SimpleMailMessage();
            message.setFrom(fromEmail);
            message.setTo(toEmail);
            message.setSubject("订单配送完成");
            message.setText(String.format(
                "亲爱的 %s，\n\n" +
                "您的订单已送达！\n\n" +
                "订单号：%s\n\n" +
                "感谢您的订购，欢迎对商品进行评价。\n\n" +
                "订餐系统团队",
                username, orderNumber
            ));
            
            mailSender.send(message);
            logger.info("订单配送邮件发送成功: {}", toEmail);
        } catch (Exception e) {
            logger.error("发送订单配送邮件失败: {}", toEmail, e);
        }
    }
}
