package com.foodorder.recommendation.evaluation;

import com.foodorder.entity.Meal;
import com.foodorder.entity.Order;
import com.foodorder.entity.Rating;
import com.foodorder.entity.User;
import com.foodorder.repository.OrderRepository;
import com.foodorder.repository.RatingRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 推荐效果评估器
 */
@Component
public class RecommendationEvaluator {
    
    private static final Logger logger = LoggerFactory.getLogger(RecommendationEvaluator.class);
    
    @Autowired
    private OrderRepository orderRepository;
    
    @Autowired
    private RatingRepository ratingRepository;
    
    /**
     * 计算推荐准确率
     * @param user 用户
     * @param recommendations 推荐列表
     * @param actualPurchases 实际购买列表
     * @return 准确率
     */
    public double calculatePrecision(User user, List<Meal> recommendations, List<Meal> actualPurchases) {
        if (recommendations.isEmpty()) {
            return 0.0;
        }
        
        Set<Long> recommendedIds = recommendations.stream()
            .map(Meal::getId)
            .collect(Collectors.toSet());
        
        Set<Long> purchasedIds = actualPurchases.stream()
            .map(Meal::getId)
            .collect(Collectors.toSet());
        
        long hits = recommendedIds.stream()
            .filter(purchasedIds::contains)
            .count();
        
        double precision = (double) hits / recommendations.size();
        logger.debug("用户 {} 推荐准确率: {}", user.getUsername(), precision);
        
        return precision;
    }
    
    /**
     * 计算推荐召回率
     * @param user 用户
     * @param recommendations 推荐列表
     * @param actualPurchases 实际购买列表
     * @return 召回率
     */
    public double calculateRecall(User user, List<Meal> recommendations, List<Meal> actualPurchases) {
        if (actualPurchases.isEmpty()) {
            return 0.0;
        }
        
        Set<Long> recommendedIds = recommendations.stream()
            .map(Meal::getId)
            .collect(Collectors.toSet());
        
        Set<Long> purchasedIds = actualPurchases.stream()
            .map(Meal::getId)
            .collect(Collectors.toSet());
        
        long hits = purchasedIds.stream()
            .filter(recommendedIds::contains)
            .count();
        
        double recall = (double) hits / actualPurchases.size();
        logger.debug("用户 {} 推荐召回率: {}", user.getUsername(), recall);
        
        return recall;
    }
    
    /**
     * 计算F1分数
     * @param precision 准确率
     * @param recall 召回率
     * @return F1分数
     */
    public double calculateF1Score(double precision, double recall) {
        if (precision + recall == 0) {
            return 0.0;
        }
        return 2 * (precision * recall) / (precision + recall);
    }
    
    /**
     * 计算推荐多样性
     * @param recommendations 推荐列表
     * @return 多样性分数
     */
    public double calculateDiversity(List<Meal> recommendations) {
        if (recommendations.size() <= 1) {
            return 0.0;
        }
        
        // 计算分类多样性
        Set<Long> categories = recommendations.stream()
            .map(meal -> meal.getCategory().getId())
            .collect(Collectors.toSet());
        
        double categoryDiversity = (double) categories.size() / recommendations.size();
        
        // 计算标签多样性
        Set<String> allTags = recommendations.stream()
            .flatMap(meal -> meal.getTags().stream())
            .collect(Collectors.toSet());
        
        double tagDiversity = (double) allTags.size() / 
            recommendations.stream().mapToInt(meal -> meal.getTags().size()).sum();
        
        return (categoryDiversity + tagDiversity) / 2.0;
    }
    
    /**
     * 计算推荐新颖性
     * @param user 用户
     * @param recommendations 推荐列表
     * @return 新颖性分数
     */
    public double calculateNovelty(User user, List<Meal> recommendations) {
        // 获取用户历史评分的商品
        List<Rating> userRatings = ratingRepository.findByUser(user);
        Set<Long> ratedMealIds = userRatings.stream()
            .map(rating -> rating.getMeal().getId())
            .collect(Collectors.toSet());
        
        // 获取用户历史购买的商品
        List<Order> userOrders = orderRepository.findByCustomer(user);
        Set<Long> purchasedMealIds = userOrders.stream()
            .flatMap(order -> order.getOrderItems().stream())
            .map(item -> item.getMeal().getId())
            .collect(Collectors.toSet());
        
        Set<Long> knownMealIds = new HashSet<>(ratedMealIds);
        knownMealIds.addAll(purchasedMealIds);
        
        if (recommendations.isEmpty()) {
            return 0.0;
        }
        
        long novelItems = recommendations.stream()
            .filter(meal -> !knownMealIds.contains(meal.getId()))
            .count();
        
        return (double) novelItems / recommendations.size();
    }
    
    /**
     * 计算推荐覆盖率
     * @param allRecommendations 所有用户的推荐结果
     * @param totalItems 总商品数
     * @return 覆盖率
     */
    public double calculateCoverage(List<List<Meal>> allRecommendations, int totalItems) {
        Set<Long> recommendedItems = allRecommendations.stream()
            .flatMap(List::stream)
            .map(Meal::getId)
            .collect(Collectors.toSet());
        
        return (double) recommendedItems.size() / totalItems;
    }
    
    /**
     * 计算推荐点击率
     * @param user 用户
     * @param recommendations 推荐列表
     * @param timeWindow 时间窗口（小时）
     * @return 点击率
     */
    public double calculateClickThroughRate(User user, List<Meal> recommendations, int timeWindow) {
        LocalDateTime startTime = LocalDateTime.now().minusHours(timeWindow);
        
        // 获取时间窗口内的订单
        List<Order> recentOrders = orderRepository.findByCustomerAndCreatedAtBetween(
            user, startTime, LocalDateTime.now());
        
        Set<Long> clickedMealIds = recentOrders.stream()
            .flatMap(order -> order.getOrderItems().stream())
            .map(item -> item.getMeal().getId())
            .collect(Collectors.toSet());
        
        if (recommendations.isEmpty()) {
            return 0.0;
        }
        
        long clicks = recommendations.stream()
            .filter(meal -> clickedMealIds.contains(meal.getId()))
            .count();
        
        return (double) clicks / recommendations.size();
    }
    
    /**
     * 生成推荐评估报告
     * @param user 用户
     * @param recommendations 推荐列表
     * @param actualPurchases 实际购买列表
     * @return 评估报告
     */
    public RecommendationEvaluationReport generateEvaluationReport(
            User user, List<Meal> recommendations, List<Meal> actualPurchases) {
        
        double precision = calculatePrecision(user, recommendations, actualPurchases);
        double recall = calculateRecall(user, recommendations, actualPurchases);
        double f1Score = calculateF1Score(precision, recall);
        double diversity = calculateDiversity(recommendations);
        double novelty = calculateNovelty(user, recommendations);
        double ctr = calculateClickThroughRate(user, recommendations, 24); // 24小时窗口
        
        return new RecommendationEvaluationReport(
            user.getId(),
            precision,
            recall,
            f1Score,
            diversity,
            novelty,
            ctr,
            recommendations.size(),
            actualPurchases.size(),
            LocalDateTime.now()
        );
    }
    
    /**
     * 推荐评估报告
     */
    public static class RecommendationEvaluationReport {
        private final Long userId;
        private final double precision;
        private final double recall;
        private final double f1Score;
        private final double diversity;
        private final double novelty;
        private final double clickThroughRate;
        private final int recommendationCount;
        private final int actualPurchaseCount;
        private final LocalDateTime evaluationTime;
        
        public RecommendationEvaluationReport(Long userId, double precision, double recall, 
                                            double f1Score, double diversity, double novelty,
                                            double clickThroughRate, int recommendationCount,
                                            int actualPurchaseCount, LocalDateTime evaluationTime) {
            this.userId = userId;
            this.precision = precision;
            this.recall = recall;
            this.f1Score = f1Score;
            this.diversity = diversity;
            this.novelty = novelty;
            this.clickThroughRate = clickThroughRate;
            this.recommendationCount = recommendationCount;
            this.actualPurchaseCount = actualPurchaseCount;
            this.evaluationTime = evaluationTime;
        }
        
        // Getters
        public Long getUserId() { return userId; }
        public double getPrecision() { return precision; }
        public double getRecall() { return recall; }
        public double getF1Score() { return f1Score; }
        public double getDiversity() { return diversity; }
        public double getNovelty() { return novelty; }
        public double getClickThroughRate() { return clickThroughRate; }
        public int getRecommendationCount() { return recommendationCount; }
        public int getActualPurchaseCount() { return actualPurchaseCount; }
        public LocalDateTime getEvaluationTime() { return evaluationTime; }
        
        @Override
        public String toString() {
            return String.format(
                "RecommendationEvaluationReport{userId=%d, precision=%.3f, recall=%.3f, " +
                "f1Score=%.3f, diversity=%.3f, novelty=%.3f, ctr=%.3f}",
                userId, precision, recall, f1Score, diversity, novelty, clickThroughRate
            );
        }
    }
}
