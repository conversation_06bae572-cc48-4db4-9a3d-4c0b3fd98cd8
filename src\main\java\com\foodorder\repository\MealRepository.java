package com.foodorder.repository;

import com.foodorder.entity.Category;
import com.foodorder.entity.Meal;
import com.foodorder.entity.MealStatus;
import com.foodorder.entity.Merchant;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;

@Repository
public interface MealRepository extends JpaRepository<Meal, Long> {
    
    List<Meal> findByMerchant(Merchant merchant);
    
    Page<Meal> findByMerchant(Merchant merchant, Pageable pageable);
    
    List<Meal> findByCategory(Category category);
    
    Page<Meal> findByCategory(Category category, Pageable pageable);
    
    List<Meal> findByStatus(MealStatus status);
    
    Page<Meal> findByStatus(MealStatus status, Pageable pageable);
    
    List<Meal> findByEnabledTrue();
    
    Page<Meal> findByEnabledTrue(Pageable pageable);
    
    @Query("SELECT m FROM Meal m WHERE m.status = 'AVAILABLE' AND m.enabled = true AND m.stock > 0")
    List<Meal> findAvailableMeals();
    
    @Query("SELECT m FROM Meal m WHERE m.status = 'AVAILABLE' AND m.enabled = true AND m.stock > 0")
    Page<Meal> findAvailableMeals(Pageable pageable);
    
    @Query("SELECT m FROM Meal m WHERE m.merchant = :merchant AND m.status = 'AVAILABLE' AND m.enabled = true AND m.stock > 0")
    List<Meal> findAvailableMealsByMerchant(@Param("merchant") Merchant merchant);
    
    @Query("SELECT m FROM Meal m WHERE m.merchant = :merchant AND m.status = 'AVAILABLE' AND m.enabled = true AND m.stock > 0")
    Page<Meal> findAvailableMealsByMerchant(@Param("merchant") Merchant merchant, Pageable pageable);
    
    @Query("SELECT m FROM Meal m WHERE m.category = :category AND m.status = 'AVAILABLE' AND m.enabled = true AND m.stock > 0")
    Page<Meal> findAvailableMealsByCategory(@Param("category") Category category, Pageable pageable);
    
    @Query("SELECT m FROM Meal m WHERE m.deleted = false AND " +
           "(LOWER(m.name) LIKE LOWER(CONCAT('%', :keyword, '%')) OR " +
           "LOWER(m.description) LIKE LOWER(CONCAT('%', :keyword, '%')))")
    Page<Meal> searchMeals(@Param("keyword") String keyword, Pageable pageable);
    
    @Query("SELECT m FROM Meal m WHERE m.status = 'AVAILABLE' AND m.enabled = true AND m.stock > 0 AND " +
           "(LOWER(m.name) LIKE LOWER(CONCAT('%', :keyword, '%')) OR " +
           "LOWER(m.description) LIKE LOWER(CONCAT('%', :keyword, '%')))")
    Page<Meal> searchAvailableMeals(@Param("keyword") String keyword, Pageable pageable);
    
    @Query("SELECT m FROM Meal m WHERE m.status = 'AVAILABLE' AND m.enabled = true AND m.stock > 0 " +
           "ORDER BY m.salesCount DESC")
    List<Meal> findPopularMeals(Pageable pageable);
    
    @Query("SELECT m FROM Meal m WHERE m.status = 'AVAILABLE' AND m.enabled = true AND m.stock > 0 " +
           "ORDER BY m.rating DESC, m.ratingCount DESC")
    List<Meal> findTopRatedMeals(Pageable pageable);
    
    @Query("SELECT m FROM Meal m WHERE m.status = 'AVAILABLE' AND m.enabled = true AND m.stock > 0 " +
           "AND m.price BETWEEN :minPrice AND :maxPrice")
    Page<Meal> findMealsByPriceRange(@Param("minPrice") BigDecimal minPrice, 
                                     @Param("maxPrice") BigDecimal maxPrice, 
                                     Pageable pageable);
    
    @Query("SELECT m FROM Meal m WHERE m.status = 'AVAILABLE' AND m.enabled = true AND m.stock > 0 " +
           "AND m.spicyLevel = :spicyLevel")
    Page<Meal> findMealsBySpicyLevel(@Param("spicyLevel") Integer spicyLevel, Pageable pageable);
    
    @Query("SELECT DISTINCT m FROM Meal m JOIN m.tags t WHERE t IN :tags AND " +
           "m.status = 'AVAILABLE' AND m.enabled = true AND m.stock > 0")
    Page<Meal> findMealsByTags(@Param("tags") List<String> tags, Pageable pageable);
}
