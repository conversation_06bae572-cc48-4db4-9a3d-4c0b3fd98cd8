package com.foodorder.service;

import com.foodorder.common.exception.BusinessException;
import com.foodorder.common.exception.ResourceNotFoundException;
import com.foodorder.dto.rating.CreateRatingRequest;
import com.foodorder.dto.rating.RatingDto;
import com.foodorder.entity.*;
import com.foodorder.repository.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
public class RatingService {
    
    private static final Logger logger = LoggerFactory.getLogger(RatingService.class);
    
    @Autowired
    private RatingRepository ratingRepository;
    
    @Autowired
    private MealRepository mealRepository;
    
    @Autowired
    private MerchantRepository merchantRepository;
    
    @Autowired
    private OrderRepository orderRepository;
    
    @Autowired
    private AuthService authService;
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    @Transactional
    public RatingDto createRating(CreateRatingRequest request) {
        User user = authService.getCurrentUser();
        if (user == null) {
            throw new BusinessException("用户未登录");
        }
        
        // 验证商品
        Meal meal = mealRepository.findById(request.getMealId())
            .orElseThrow(() -> new ResourceNotFoundException("商品不存在"));
        
        Order order = null;
        if (request.getOrderId() != null) {
            order = orderRepository.findById(request.getOrderId())
                .orElseThrow(() -> new ResourceNotFoundException("订单不存在"));
            
            // 验证订单是否属于当前用户
            if (!order.getCustomer().getId().equals(user.getId())) {
                throw new BusinessException("无权对此订单进行评价");
            }
            
            // 验证订单是否已完成
            if (order.getStatus() != OrderStatus.DELIVERED) {
                throw new BusinessException("只能对已完成的订单进行评价");
            }
            
            // 验证是否已经评价过
            if (ratingRepository.existsByUserAndMealAndOrder(user, meal, order)) {
                throw new BusinessException("您已经对此商品进行过评价");
            }
        }
        
        // 创建评分
        Rating rating = new Rating();
        rating.setUser(user);
        rating.setMeal(meal);
        rating.setMerchant(meal.getMerchant());
        rating.setOrder(order);
        rating.setScore(request.getScore());
        rating.setReview(request.getReview());
        rating.setAnonymous(request.getAnonymous());
        
        Rating savedRating = ratingRepository.save(rating);
        
        // 更新商品评分统计
        meal.updateRating(request.getScore());
        mealRepository.save(meal);
        
        // 更新商家评分统计
        Merchant merchant = meal.getMerchant();
        merchant.updateRating(request.getScore());
        merchantRepository.save(merchant);
        
        // 缓存用户最近评分（用于实时推荐）
        cacheUserRecentRating(user.getId(), savedRating);
        
        logger.info("用户 {} 对商品 {} 评分: {}", user.getUsername(), meal.getName(), request.getScore());
        
        return RatingDto.fromRating(savedRating);
    }
    
    public Page<RatingDto> getUserRatings(Pageable pageable) {
        User user = authService.getCurrentUser();
        if (user == null) {
            throw new BusinessException("用户未登录");
        }
        
        Page<Rating> ratings = ratingRepository.findByUserOrderByCreatedAtDesc(user, pageable);
        return ratings.map(RatingDto::fromRating);
    }
    
    public Page<RatingDto> getMealRatings(Long mealId, Pageable pageable) {
        Meal meal = mealRepository.findById(mealId)
            .orElseThrow(() -> new ResourceNotFoundException("商品不存在"));
        
        Page<Rating> ratings = ratingRepository.findByMealOrderByCreatedAtDesc(meal, pageable);
        return ratings.map(RatingDto::fromRating);
    }
    
    public Page<RatingDto> getMerchantRatings(Long merchantId, Pageable pageable) {
        Merchant merchant = merchantRepository.findById(merchantId)
            .orElseThrow(() -> new ResourceNotFoundException("商家不存在"));
        
        Page<Rating> ratings = ratingRepository.findByMerchantOrderByCreatedAtDesc(merchant, pageable);
        return ratings.map(RatingDto::fromRating);
    }
    
    public Page<RatingDto> getRatingsWithReviews(Pageable pageable) {
        Page<Rating> ratings = ratingRepository.findRatingsWithReviews(pageable);
        return ratings.map(RatingDto::fromRating);
    }
    
    public Page<RatingDto> getMealRatingsWithReviews(Long mealId, Pageable pageable) {
        Meal meal = mealRepository.findById(mealId)
            .orElseThrow(() -> new ResourceNotFoundException("商品不存在"));
        
        Page<Rating> ratings = ratingRepository.findRatingsWithReviewsByMeal(meal, pageable);
        return ratings.map(RatingDto::fromRating);
    }
    
    public List<RatingDto> getUserRecentRatings(Long userId, int limit) {
        // 先从缓存获取
        String cacheKey = "user_recent_ratings:" + userId;
        List<RatingDto> cachedRatings = (List<RatingDto>) redisTemplate.opsForValue().get(cacheKey);
        
        if (cachedRatings != null && !cachedRatings.isEmpty()) {
            return cachedRatings.stream().limit(limit).collect(Collectors.toList());
        }
        
        // 缓存未命中，从数据库获取
        User user = new User();
        user.setId(userId);
        
        LocalDateTime since = LocalDateTime.now().minusDays(30); // 最近30天
        List<Rating> ratings = ratingRepository.findRecentRatingsByUser(user, since);
        
        List<RatingDto> ratingDtos = ratings.stream()
            .limit(limit)
            .map(RatingDto::fromRating)
            .collect(Collectors.toList());
        
        // 缓存结果
        redisTemplate.opsForValue().set(cacheKey, ratingDtos, 1, TimeUnit.HOURS);
        
        return ratingDtos;
    }
    
    @Transactional
    public void markRatingHelpful(Long ratingId) {
        Rating rating = ratingRepository.findById(ratingId)
            .orElseThrow(() -> new ResourceNotFoundException("评价不存在"));
        
        rating.increaseHelpfulCount();
        ratingRepository.save(rating);
    }
    
    private void cacheUserRecentRating(Long userId, Rating rating) {
        try {
            String cacheKey = "user_recent_ratings:" + userId;
            List<RatingDto> cachedRatings = (List<RatingDto>) redisTemplate.opsForValue().get(cacheKey);
            
            if (cachedRatings == null) {
                cachedRatings = getUserRecentRatings(userId, 10);
            } else {
                // 添加新评分到列表开头
                cachedRatings.add(0, RatingDto.fromRating(rating));
                // 保持列表大小不超过10
                if (cachedRatings.size() > 10) {
                    cachedRatings = cachedRatings.subList(0, 10);
                }
            }
            
            redisTemplate.opsForValue().set(cacheKey, cachedRatings, 1, TimeUnit.HOURS);
        } catch (Exception e) {
            logger.warn("缓存用户最近评分失败", e);
        }
    }
}
