package com.foodorder;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

@SpringBootApplication
@RestController
public class Application {

    public static void main(String[] args) {
        SpringApplication.run(Application.class, args);
    }

    @GetMapping("/")
    public String home() {
        return "Welcome to Food Order System! The application is running successfully.";
    }

    @GetMapping("/health")
    public String health() {
        return "OK - System is healthy";
    }

    @GetMapping("/api/test")
    public String test() {
        return "API is working correctly";
    }
}
