package com.foodorder.recommendation.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 推荐系统配置
 */
@Component
@ConfigurationProperties(prefix = "recommendation")
public class RecommendationConfig {
    
    // 个性化推荐权重配置
    private PersonalizedWeights personalizedWeights = new PersonalizedWeights();
    
    // 相似度计算配置
    private SimilarityWeights similarityWeights = new SimilarityWeights();
    
    // 推荐算法配置
    private AlgorithmConfig algorithmConfig = new AlgorithmConfig();
    
    // 缓存配置
    private CacheConfig cacheConfig = new CacheConfig();
    
    public static class PersonalizedWeights {
        private double categoryPreference = 0.4;
        private double tagPreference = 0.3;
        private double ratingWeight = 0.2;
        private double salesWeight = 0.1;
        
        // Getters and Setters
        public double getCategoryPreference() {
            return categoryPreference;
        }
        
        public void setCategoryPreference(double categoryPreference) {
            this.categoryPreference = categoryPreference;
        }
        
        public double getTagPreference() {
            return tagPreference;
        }
        
        public void setTagPreference(double tagPreference) {
            this.tagPreference = tagPreference;
        }
        
        public double getRatingWeight() {
            return ratingWeight;
        }
        
        public void setRatingWeight(double ratingWeight) {
            this.ratingWeight = ratingWeight;
        }
        
        public double getSalesWeight() {
            return salesWeight;
        }
        
        public void setSalesWeight(double salesWeight) {
            this.salesWeight = salesWeight;
        }
    }
    
    public static class SimilarityWeights {
        private double categoryWeight = 0.4;
        private double tagWeight = 0.3;
        private double priceWeight = 0.2;
        private double ratingWeight = 0.1;
        
        // Getters and Setters
        public double getCategoryWeight() {
            return categoryWeight;
        }
        
        public void setCategoryWeight(double categoryWeight) {
            this.categoryWeight = categoryWeight;
        }
        
        public double getTagWeight() {
            return tagWeight;
        }
        
        public void setTagWeight(double tagWeight) {
            this.tagWeight = tagWeight;
        }
        
        public double getPriceWeight() {
            return priceWeight;
        }
        
        public void setPriceWeight(double priceWeight) {
            this.priceWeight = priceWeight;
        }
        
        public double getRatingWeight() {
            return ratingWeight;
        }
        
        public void setRatingWeight(double ratingWeight) {
            this.ratingWeight = ratingWeight;
        }
    }
    
    public static class AlgorithmConfig {
        private double userSimilarityThreshold = 0.1;
        private int maxSimilarUsers = 10;
        private double highRatingThreshold = 4.0;
        private int recentDays = 7;
        private int maxRecommendations = 50;
        
        // Getters and Setters
        public double getUserSimilarityThreshold() {
            return userSimilarityThreshold;
        }
        
        public void setUserSimilarityThreshold(double userSimilarityThreshold) {
            this.userSimilarityThreshold = userSimilarityThreshold;
        }
        
        public int getMaxSimilarUsers() {
            return maxSimilarUsers;
        }
        
        public void setMaxSimilarUsers(int maxSimilarUsers) {
            this.maxSimilarUsers = maxSimilarUsers;
        }
        
        public double getHighRatingThreshold() {
            return highRatingThreshold;
        }
        
        public void setHighRatingThreshold(double highRatingThreshold) {
            this.highRatingThreshold = highRatingThreshold;
        }
        
        public int getRecentDays() {
            return recentDays;
        }
        
        public void setRecentDays(int recentDays) {
            this.recentDays = recentDays;
        }
        
        public int getMaxRecommendations() {
            return maxRecommendations;
        }
        
        public void setMaxRecommendations(int maxRecommendations) {
            this.maxRecommendations = maxRecommendations;
        }
    }
    
    public static class CacheConfig {
        private int personalizedCacheTtl = 3600; // 1小时
        private int popularCacheTtl = 1800; // 30分钟
        private int similarCacheTtl = 7200; // 2小时
        private int realtimeCacheTtl = 600; // 10分钟
        
        // Getters and Setters
        public int getPersonalizedCacheTtl() {
            return personalizedCacheTtl;
        }
        
        public void setPersonalizedCacheTtl(int personalizedCacheTtl) {
            this.personalizedCacheTtl = personalizedCacheTtl;
        }
        
        public int getPopularCacheTtl() {
            return popularCacheTtl;
        }
        
        public void setPopularCacheTtl(int popularCacheTtl) {
            this.popularCacheTtl = popularCacheTtl;
        }
        
        public int getSimilarCacheTtl() {
            return similarCacheTtl;
        }
        
        public void setSimilarCacheTtl(int similarCacheTtl) {
            this.similarCacheTtl = similarCacheTtl;
        }
        
        public int getRealtimeCacheTtl() {
            return realtimeCacheTtl;
        }
        
        public void setRealtimeCacheTtl(int realtimeCacheTtl) {
            this.realtimeCacheTtl = realtimeCacheTtl;
        }
    }
    
    // Main class getters and setters
    public PersonalizedWeights getPersonalizedWeights() {
        return personalizedWeights;
    }
    
    public void setPersonalizedWeights(PersonalizedWeights personalizedWeights) {
        this.personalizedWeights = personalizedWeights;
    }
    
    public SimilarityWeights getSimilarityWeights() {
        return similarityWeights;
    }
    
    public void setSimilarityWeights(SimilarityWeights similarityWeights) {
        this.similarityWeights = similarityWeights;
    }
    
    public AlgorithmConfig getAlgorithmConfig() {
        return algorithmConfig;
    }
    
    public void setAlgorithmConfig(AlgorithmConfig algorithmConfig) {
        this.algorithmConfig = algorithmConfig;
    }
    
    public CacheConfig getCacheConfig() {
        return cacheConfig;
    }
    
    public void setCacheConfig(CacheConfig cacheConfig) {
        this.cacheConfig = cacheConfig;
    }
}
