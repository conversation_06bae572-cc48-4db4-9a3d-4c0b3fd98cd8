package com.foodorder.dto.meal;

import com.foodorder.entity.Meal;
import com.foodorder.entity.MealStatus;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Set;

public class MealDto {
    
    private Long id;
    private Long merchantId;
    private String merchantName;
    private Long categoryId;
    private String categoryName;
    private String name;
    private String description;
    private BigDecimal price;
    private BigDecimal originalPrice;
    private String imageUrl;
    private Set<String> imageUrls;
    private MealStatus status;
    private Integer stock;
    private Integer salesCount;
    private BigDecimal rating;
    private Integer ratingCount;
    private Set<String> tags;
    private Integer spicyLevel;
    private Integer calories;
    private Integer preparationTime;
    private Boolean enabled;
    private LocalDateTime createdAt;
    
    public MealDto() {}
    
    public MealDto(Meal meal) {
        this.id = meal.getId();
        this.merchantId = meal.getMerchant().getId();
        this.merchantName = meal.getMerchant().getName();
        this.categoryId = meal.getCategory().getId();
        this.categoryName = meal.getCategory().getName();
        this.name = meal.getName();
        this.description = meal.getDescription();
        this.price = meal.getPrice();
        this.originalPrice = meal.getOriginalPrice();
        this.imageUrl = meal.getImageUrl();
        this.imageUrls = meal.getImageUrls();
        this.status = meal.getStatus();
        this.stock = meal.getStock();
        this.salesCount = meal.getSalesCount();
        this.rating = meal.getRating();
        this.ratingCount = meal.getRatingCount();
        this.tags = meal.getTags();
        this.spicyLevel = meal.getSpicyLevel();
        this.calories = meal.getCalories();
        this.preparationTime = meal.getPreparationTime();
        this.enabled = meal.getEnabled();
        this.createdAt = meal.getCreatedAt();
    }
    
    public static MealDto fromMeal(Meal meal) {
        return new MealDto(meal);
    }
    
    // Getters and Setters
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public Long getMerchantId() {
        return merchantId;
    }
    
    public void setMerchantId(Long merchantId) {
        this.merchantId = merchantId;
    }
    
    public String getMerchantName() {
        return merchantName;
    }
    
    public void setMerchantName(String merchantName) {
        this.merchantName = merchantName;
    }
    
    public Long getCategoryId() {
        return categoryId;
    }
    
    public void setCategoryId(Long categoryId) {
        this.categoryId = categoryId;
    }
    
    public String getCategoryName() {
        return categoryName;
    }
    
    public void setCategoryName(String categoryName) {
        this.categoryName = categoryName;
    }
    
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public BigDecimal getPrice() {
        return price;
    }
    
    public void setPrice(BigDecimal price) {
        this.price = price;
    }
    
    public BigDecimal getOriginalPrice() {
        return originalPrice;
    }
    
    public void setOriginalPrice(BigDecimal originalPrice) {
        this.originalPrice = originalPrice;
    }
    
    public String getImageUrl() {
        return imageUrl;
    }
    
    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }
    
    public Set<String> getImageUrls() {
        return imageUrls;
    }
    
    public void setImageUrls(Set<String> imageUrls) {
        this.imageUrls = imageUrls;
    }
    
    public MealStatus getStatus() {
        return status;
    }
    
    public void setStatus(MealStatus status) {
        this.status = status;
    }
    
    public Integer getStock() {
        return stock;
    }
    
    public void setStock(Integer stock) {
        this.stock = stock;
    }
    
    public Integer getSalesCount() {
        return salesCount;
    }
    
    public void setSalesCount(Integer salesCount) {
        this.salesCount = salesCount;
    }
    
    public BigDecimal getRating() {
        return rating;
    }
    
    public void setRating(BigDecimal rating) {
        this.rating = rating;
    }
    
    public Integer getRatingCount() {
        return ratingCount;
    }
    
    public void setRatingCount(Integer ratingCount) {
        this.ratingCount = ratingCount;
    }
    
    public Set<String> getTags() {
        return tags;
    }
    
    public void setTags(Set<String> tags) {
        this.tags = tags;
    }
    
    public Integer getSpicyLevel() {
        return spicyLevel;
    }
    
    public void setSpicyLevel(Integer spicyLevel) {
        this.spicyLevel = spicyLevel;
    }
    
    public Integer getCalories() {
        return calories;
    }
    
    public void setCalories(Integer calories) {
        this.calories = calories;
    }
    
    public Integer getPreparationTime() {
        return preparationTime;
    }
    
    public void setPreparationTime(Integer preparationTime) {
        this.preparationTime = preparationTime;
    }
    
    public Boolean getEnabled() {
        return enabled;
    }
    
    public void setEnabled(Boolean enabled) {
        this.enabled = enabled;
    }
    
    public LocalDateTime getCreatedAt() {
        return createdAt;
    }
    
    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }
}
