spring:
  # Database Configuration for Production
  datasource:
    url: ${DATABASE_URL:*********************************************}
    driver-class-name: com.mysql.cj.jdbc.Driver
    username: ${DB_USERNAME}
    password: ${DB_PASSWORD}
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000
  
  # JPA Configuration for Production
  jpa:
    hibernate:
      ddl-auto: validate
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQL8Dialect
        format_sql: false
  
  # Redis Configuration for Production
  data:
    redis:
      host: ${REDIS_HOST:localhost}
      port: ${REDIS_PORT:6379}
      password: ${REDIS_PASSWORD:}

# JWT Configuration for Production
jwt:
  secret: ${JWT_SECRET}
  expiration: 86400000

# Logging Configuration for Production
logging:
  level:
    com.foodorder: INFO
    org.springframework.security: WARN
    org.hibernate.SQL: WARN
    root: WARN
  file:
    name: /var/log/food-order-system/application.log
