package com.foodorder.dto.order;

import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;

public class CreateOrderItemRequest {
    
    @NotNull(message = "商品ID不能为空")
    private Long mealId;
    
    @NotNull(message = "数量不能为空")
    @Min(value = 1, message = "数量必须大于0")
    private Integer quantity;
    
    private String notes;
    
    public CreateOrderItemRequest() {}
    
    public CreateOrderItemRequest(Long mealId, Integer quantity) {
        this.mealId = mealId;
        this.quantity = quantity;
    }
    
    // Getters and Setters
    public Long getMealId() {
        return mealId;
    }
    
    public void setMealId(Long mealId) {
        this.mealId = mealId;
    }
    
    public Integer getQuantity() {
        return quantity;
    }
    
    public void setQuantity(Integer quantity) {
        this.quantity = quantity;
    }
    
    public String getNotes() {
        return notes;
    }
    
    public void setNotes(String notes) {
        this.notes = notes;
    }
}
