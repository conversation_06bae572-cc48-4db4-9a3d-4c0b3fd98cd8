package com.foodorder.dto.merchant;

import com.foodorder.entity.Merchant;
import com.foodorder.entity.MerchantStatus;

import java.math.BigDecimal;
import java.time.LocalDateTime;

public class MerchantDto {
    
    private Long id;
    private Long userId;
    private String username;
    private String name;
    private String description;
    private String address;
    private String phone;
    private String logoUrl;
    private String coverImageUrl;
    private MerchantStatus status;
    private String businessLicense;
    private String openingHours;
    private BigDecimal deliveryFee;
    private BigDecimal minOrderAmount;
    private BigDecimal rating;
    private Integer ratingCount;
    private Integer orderCount;
    private Boolean enabled;
    private LocalDateTime createdAt;
    
    public MerchantDto() {}
    
    public MerchantDto(Merchant merchant) {
        this.id = merchant.getId();
        this.userId = merchant.getUser().getId();
        this.username = merchant.getUser().getUsername();
        this.name = merchant.getName();
        this.description = merchant.getDescription();
        this.address = merchant.getAddress();
        this.phone = merchant.getPhone();
        this.logoUrl = merchant.getLogoUrl();
        this.coverImageUrl = merchant.getCoverImageUrl();
        this.status = merchant.getStatus();
        this.businessLicense = merchant.getBusinessLicense();
        this.openingHours = merchant.getOpeningHours();
        this.deliveryFee = merchant.getDeliveryFee();
        this.minOrderAmount = merchant.getMinOrderAmount();
        this.rating = merchant.getRating();
        this.ratingCount = merchant.getRatingCount();
        this.orderCount = merchant.getOrderCount();
        this.enabled = merchant.getEnabled();
        this.createdAt = merchant.getCreatedAt();
    }
    
    public static MerchantDto fromMerchant(Merchant merchant) {
        return new MerchantDto(merchant);
    }
    
    // Getters and Setters
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public Long getUserId() {
        return userId;
    }
    
    public void setUserId(Long userId) {
        this.userId = userId;
    }
    
    public String getUsername() {
        return username;
    }
    
    public void setUsername(String username) {
        this.username = username;
    }
    
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public String getAddress() {
        return address;
    }
    
    public void setAddress(String address) {
        this.address = address;
    }
    
    public String getPhone() {
        return phone;
    }
    
    public void setPhone(String phone) {
        this.phone = phone;
    }
    
    public String getLogoUrl() {
        return logoUrl;
    }
    
    public void setLogoUrl(String logoUrl) {
        this.logoUrl = logoUrl;
    }
    
    public String getCoverImageUrl() {
        return coverImageUrl;
    }
    
    public void setCoverImageUrl(String coverImageUrl) {
        this.coverImageUrl = coverImageUrl;
    }
    
    public MerchantStatus getStatus() {
        return status;
    }
    
    public void setStatus(MerchantStatus status) {
        this.status = status;
    }
    
    public String getBusinessLicense() {
        return businessLicense;
    }
    
    public void setBusinessLicense(String businessLicense) {
        this.businessLicense = businessLicense;
    }
    
    public String getOpeningHours() {
        return openingHours;
    }
    
    public void setOpeningHours(String openingHours) {
        this.openingHours = openingHours;
    }
    
    public BigDecimal getDeliveryFee() {
        return deliveryFee;
    }
    
    public void setDeliveryFee(BigDecimal deliveryFee) {
        this.deliveryFee = deliveryFee;
    }
    
    public BigDecimal getMinOrderAmount() {
        return minOrderAmount;
    }
    
    public void setMinOrderAmount(BigDecimal minOrderAmount) {
        this.minOrderAmount = minOrderAmount;
    }
    
    public BigDecimal getRating() {
        return rating;
    }
    
    public void setRating(BigDecimal rating) {
        this.rating = rating;
    }
    
    public Integer getRatingCount() {
        return ratingCount;
    }
    
    public void setRatingCount(Integer ratingCount) {
        this.ratingCount = ratingCount;
    }
    
    public Integer getOrderCount() {
        return orderCount;
    }
    
    public void setOrderCount(Integer orderCount) {
        this.orderCount = orderCount;
    }
    
    public Boolean getEnabled() {
        return enabled;
    }
    
    public void setEnabled(Boolean enabled) {
        this.enabled = enabled;
    }
    
    public LocalDateTime getCreatedAt() {
        return createdAt;
    }
    
    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }
}
