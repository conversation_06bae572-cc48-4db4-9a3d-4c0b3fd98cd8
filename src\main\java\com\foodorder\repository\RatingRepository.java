package com.foodorder.repository;

import com.foodorder.entity.*;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface RatingRepository extends JpaRepository<Rating, Long> {
    
    List<Rating> findByUser(User user);
    
    Page<Rating> findByUser(User user, Pageable pageable);
    
    List<Rating> findByMeal(Meal meal);
    
    Page<Rating> findByMeal(Meal meal, Pageable pageable);
    
    List<Rating> findByMerchant(Merchant merchant);
    
    Page<Rating> findByMerchant(Merchant merchant, Pageable pageable);
    
    Optional<Rating> findByUserAndMealAndOrder(User user, Meal meal, Order order);
    
    boolean existsByUserAndMealAndOrder(User user, Meal meal, Order order);
    
    @Query("SELECT r FROM Rating r WHERE r.user = :user ORDER BY r.createdAt DESC")
    List<Rating> findByUserOrderByCreatedAtDesc(@Param("user") User user);
    
    @Query("SELECT r FROM Rating r WHERE r.user = :user ORDER BY r.createdAt DESC")
    Page<Rating> findByUserOrderByCreatedAtDesc(@Param("user") User user, Pageable pageable);
    
    @Query("SELECT r FROM Rating r WHERE r.meal = :meal ORDER BY r.createdAt DESC")
    Page<Rating> findByMealOrderByCreatedAtDesc(@Param("meal") Meal meal, Pageable pageable);
    
    @Query("SELECT r FROM Rating r WHERE r.merchant = :merchant ORDER BY r.createdAt DESC")
    Page<Rating> findByMerchantOrderByCreatedAtDesc(@Param("merchant") Merchant merchant, Pageable pageable);
    
    @Query("SELECT r FROM Rating r WHERE r.user = :user AND r.createdAt >= :since ORDER BY r.createdAt DESC")
    List<Rating> findRecentRatingsByUser(@Param("user") User user, @Param("since") LocalDateTime since);
    
    @Query("SELECT r FROM Rating r WHERE r.user = :user ORDER BY r.createdAt DESC")
    List<Rating> findRecentRatingsByUser(@Param("user") User user, Pageable pageable);
    
    @Query("SELECT AVG(r.score) FROM Rating r WHERE r.meal = :meal")
    BigDecimal findAverageScoreByMeal(@Param("meal") Meal meal);
    
    @Query("SELECT AVG(r.score) FROM Rating r WHERE r.merchant = :merchant")
    BigDecimal findAverageScoreByMerchant(@Param("merchant") Merchant merchant);
    
    @Query("SELECT COUNT(r) FROM Rating r WHERE r.meal = :meal")
    Long countByMeal(@Param("meal") Meal meal);
    
    @Query("SELECT COUNT(r) FROM Rating r WHERE r.merchant = :merchant")
    Long countByMerchant(@Param("merchant") Merchant merchant);
    
    @Query("SELECT COUNT(r) FROM Rating r WHERE r.user = :user")
    Long countByUser(@Param("user") User user);
    
    @Query("SELECT r.score, COUNT(r) FROM Rating r WHERE r.meal = :meal GROUP BY r.score ORDER BY r.score DESC")
    List<Object[]> findScoreDistributionByMeal(@Param("meal") Meal meal);
    
    @Query("SELECT r.score, COUNT(r) FROM Rating r WHERE r.merchant = :merchant GROUP BY r.score ORDER BY r.score DESC")
    List<Object[]> findScoreDistributionByMerchant(@Param("merchant") Merchant merchant);
    
    @Query("SELECT r FROM Rating r WHERE r.review IS NOT NULL AND r.review != '' ORDER BY r.createdAt DESC")
    Page<Rating> findRatingsWithReviews(Pageable pageable);
    
    @Query("SELECT r FROM Rating r WHERE r.meal = :meal AND r.review IS NOT NULL AND r.review != '' ORDER BY r.createdAt DESC")
    Page<Rating> findRatingsWithReviewsByMeal(@Param("meal") Meal meal, Pageable pageable);
}
