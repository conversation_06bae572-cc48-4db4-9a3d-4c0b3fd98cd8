package com.foodorder.repository;

import com.foodorder.entity.Merchant;
import com.foodorder.entity.Order;
import com.foodorder.entity.OrderStatus;
import com.foodorder.entity.User;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface OrderRepository extends JpaRepository<Order, Long> {
    
    Optional<Order> findByOrderNumber(String orderNumber);
    
    List<Order> findByCustomer(User customer);
    
    Page<Order> findByCustomer(User customer, Pageable pageable);
    
    List<Order> findByMerchant(Merchant merchant);
    
    Page<Order> findByMerchant(Merchant merchant, Pageable pageable);
    
    List<Order> findByStatus(OrderStatus status);
    
    Page<Order> findByStatus(OrderStatus status, Pageable pageable);
    
    List<Order> findByCustomerAndStatus(User customer, OrderStatus status);
    
    Page<Order> findByCustomerAndStatus(User customer, OrderStatus status, Pageable pageable);
    
    List<Order> findByMerchantAndStatus(Merchant merchant, OrderStatus status);
    
    Page<Order> findByMerchantAndStatus(Merchant merchant, OrderStatus status, Pageable pageable);
    
    @Query("SELECT o FROM Order o WHERE o.customer = :customer ORDER BY o.createdAt DESC")
    Page<Order> findByCustomerOrderByCreatedAtDesc(@Param("customer") User customer, Pageable pageable);
    
    @Query("SELECT o FROM Order o WHERE o.merchant = :merchant ORDER BY o.createdAt DESC")
    Page<Order> findByMerchantOrderByCreatedAtDesc(@Param("merchant") Merchant merchant, Pageable pageable);
    
    @Query("SELECT o FROM Order o WHERE o.createdAt BETWEEN :startTime AND :endTime")
    List<Order> findByCreatedAtBetween(@Param("startTime") LocalDateTime startTime, 
                                       @Param("endTime") LocalDateTime endTime);
    
    @Query("SELECT o FROM Order o WHERE o.merchant = :merchant AND o.createdAt BETWEEN :startTime AND :endTime")
    List<Order> findByMerchantAndCreatedAtBetween(@Param("merchant") Merchant merchant,
                                                  @Param("startTime") LocalDateTime startTime,
                                                  @Param("endTime") LocalDateTime endTime);
    
    @Query("SELECT COUNT(o) FROM Order o WHERE o.merchant = :merchant")
    Long countByMerchant(@Param("merchant") Merchant merchant);
    
    @Query("SELECT COUNT(o) FROM Order o WHERE o.customer = :customer")
    Long countByCustomer(@Param("customer") User customer);
    
    @Query("SELECT COUNT(o) FROM Order o WHERE o.status = :status")
    Long countByStatus(@Param("status") OrderStatus status);
    
    @Query("SELECT SUM(o.totalAmount) FROM Order o WHERE o.merchant = :merchant AND o.status = 'DELIVERED'")
    BigDecimal sumTotalAmountByMerchant(@Param("merchant") Merchant merchant);
    
    @Query("SELECT SUM(o.totalAmount) FROM Order o WHERE o.merchant = :merchant AND o.status = 'DELIVERED' " +
           "AND o.createdAt BETWEEN :startTime AND :endTime")
    BigDecimal sumTotalAmountByMerchantAndDateRange(@Param("merchant") Merchant merchant,
                                                    @Param("startTime") LocalDateTime startTime,
                                                    @Param("endTime") LocalDateTime endTime);
    
    @Query("SELECT o FROM Order o WHERE o.deleted = false")
    Page<Order> findAllActive(Pageable pageable);
}
