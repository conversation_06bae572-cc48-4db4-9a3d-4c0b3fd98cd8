package com.foodorder.service;

import com.foodorder.common.exception.BusinessException;
import com.foodorder.common.exception.ResourceNotFoundException;
import com.foodorder.dto.order.CreateOrderRequest;
import com.foodorder.dto.order.OrderDto;
import com.foodorder.entity.*;
import com.foodorder.repository.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.UUID;

@Service
public class OrderService {
    
    private static final Logger logger = LoggerFactory.getLogger(OrderService.class);
    
    @Autowired
    private OrderRepository orderRepository;
    
    @Autowired
    private MealRepository mealRepository;
    
    @Autowired
    private MerchantRepository merchantRepository;
    
    @Autowired
    private UserRepository userRepository;
    
    @Autowired
    private AuthService authService;
    
    @Autowired
    private EmailService emailService;
    
    @Transactional
    public OrderDto createOrder(CreateOrderRequest request) {
        User customer = authService.getCurrentUser();
        if (customer == null) {
            throw new BusinessException("用户未登录");
        }
        
        // 验证商家
        Merchant merchant = merchantRepository.findById(request.getMerchantId())
            .orElseThrow(() -> new ResourceNotFoundException("商家不存在"));
        
        if (!merchant.getEnabled() || merchant.getStatus() != MerchantStatus.APPROVED) {
            throw new BusinessException("商家暂不可用");
        }
        
        // 创建订单
        Order order = new Order();
        order.setOrderNumber(generateOrderNumber());
        order.setCustomer(customer);
        order.setMerchant(merchant);
        order.setDeliveryAddress(request.getDeliveryAddress());
        order.setDeliveryPhone(request.getDeliveryPhone());
        order.setDeliveryContact(request.getDeliveryContact());
        order.setNotes(request.getNotes());
        order.setPaymentMethod(request.getPaymentMethod());
        order.setDeliveryFee(merchant.getDeliveryFee());
        
        // 添加订单项
        for (var itemRequest : request.getOrderItems()) {
            Meal meal = mealRepository.findById(itemRequest.getMealId())
                .orElseThrow(() -> new ResourceNotFoundException("商品不存在: " + itemRequest.getMealId()));
            
            // 验证商品可用性
            if (!meal.isAvailable()) {
                throw new BusinessException("商品不可用: " + meal.getName());
            }
            
            // 验证库存
            if (meal.getStock() < itemRequest.getQuantity()) {
                throw new BusinessException("商品库存不足: " + meal.getName());
            }
            
            // 验证商品属于同一商家
            if (!meal.getMerchant().getId().equals(merchant.getId())) {
                throw new BusinessException("订单中的商品必须来自同一商家");
            }
            
            OrderItem orderItem = new OrderItem(order, meal, itemRequest.getQuantity());
            orderItem.setNotes(itemRequest.getNotes());
            order.addOrderItem(orderItem);
            
            // 减少库存
            meal.decreaseStock(itemRequest.getQuantity());
            mealRepository.save(meal);
        }
        
        // 验证最小订单金额
        if (order.getSubtotal().compareTo(merchant.getMinOrderAmount()) < 0) {
            throw new BusinessException("订单金额不满足最小订单要求: " + merchant.getMinOrderAmount());
        }
        
        // 设置预计送达时间（当前时间 + 30-60分钟）
        int estimatedMinutes = 30 + (int)(Math.random() * 30);
        order.setEstimatedDeliveryTime(LocalDateTime.now().plusMinutes(estimatedMinutes));
        
        Order savedOrder = orderRepository.save(order);
        
        // 更新商家订单数量
        merchant.setOrderCount(merchant.getOrderCount() + 1);
        merchantRepository.save(merchant);
        
        logger.info("创建订单成功: {}, 客户: {}, 商家: {}", 
            savedOrder.getOrderNumber(), customer.getUsername(), merchant.getName());
        
        // 发送订单确认邮件
        try {
            emailService.sendOrderConfirmationEmail(
                customer.getEmail(), 
                customer.getUsername(), 
                savedOrder.getOrderNumber()
            );
        } catch (Exception e) {
            logger.warn("发送订单确认邮件失败", e);
        }
        
        return OrderDto.fromOrder(savedOrder);
    }
    
    public OrderDto getOrderById(Long orderId) {
        Order order = orderRepository.findById(orderId)
            .orElseThrow(() -> new ResourceNotFoundException("订单不存在"));
        
        // 验证权限：只有订单所属客户、商家或管理员可以查看
        User currentUser = authService.getCurrentUser();
        if (currentUser == null) {
            throw new BusinessException("用户未登录");
        }
        
        boolean hasAccess = order.getCustomer().getId().equals(currentUser.getId()) ||
                           order.getMerchant().getUser().getId().equals(currentUser.getId()) ||
                           currentUser.hasRole(UserRole.ADMIN);
        
        if (!hasAccess) {
            throw new BusinessException("无权访问此订单");
        }
        
        return OrderDto.fromOrder(order);
    }
    
    public OrderDto getOrderByNumber(String orderNumber) {
        Order order = orderRepository.findByOrderNumber(orderNumber)
            .orElseThrow(() -> new ResourceNotFoundException("订单不存在"));
        
        // 验证权限
        User currentUser = authService.getCurrentUser();
        if (currentUser == null) {
            throw new BusinessException("用户未登录");
        }
        
        boolean hasAccess = order.getCustomer().getId().equals(currentUser.getId()) ||
                           order.getMerchant().getUser().getId().equals(currentUser.getId()) ||
                           currentUser.hasRole(UserRole.ADMIN);
        
        if (!hasAccess) {
            throw new BusinessException("无权访问此订单");
        }
        
        return OrderDto.fromOrder(order);
    }
    
    public Page<OrderDto> getCustomerOrders(Pageable pageable) {
        User customer = authService.getCurrentUser();
        if (customer == null) {
            throw new BusinessException("用户未登录");
        }
        
        Page<Order> orders = orderRepository.findByCustomerOrderByCreatedAtDesc(customer, pageable);
        return orders.map(OrderDto::fromOrder);
    }
    
    public Page<OrderDto> getCustomerOrdersByStatus(OrderStatus status, Pageable pageable) {
        User customer = authService.getCurrentUser();
        if (customer == null) {
            throw new BusinessException("用户未登录");
        }
        
        Page<Order> orders = orderRepository.findByCustomerAndStatus(customer, status, pageable);
        return orders.map(OrderDto::fromOrder);
    }
    
    @Transactional
    public void cancelOrder(Long orderId, String reason) {
        Order order = orderRepository.findById(orderId)
            .orElseThrow(() -> new ResourceNotFoundException("订单不存在"));
        
        // 验证权限：只有订单所属客户可以取消
        User currentUser = authService.getCurrentUser();
        if (currentUser == null || !order.getCustomer().getId().equals(currentUser.getId())) {
            throw new BusinessException("无权取消此订单");
        }
        
        // 验证订单状态
        if (!order.canCancel()) {
            throw new BusinessException("订单当前状态不允许取消");
        }
        
        // 恢复库存
        for (OrderItem item : order.getOrderItems()) {
            Meal meal = item.getMeal();
            meal.setStock(meal.getStock() + item.getQuantity());
            mealRepository.save(meal);
        }
        
        // 更新订单状态
        order.setStatus(OrderStatus.CANCELLED);
        order.setCancelledAt(LocalDateTime.now());
        order.setCancelReason(reason);
        orderRepository.save(order);
        
        logger.info("订单取消成功: {}, 原因: {}", order.getOrderNumber(), reason);
    }
    
    private String generateOrderNumber() {
        return "ORD" + System.currentTimeMillis() + UUID.randomUUID().toString().substring(0, 8).toUpperCase();
    }
}
