-- 插入默认分类数据
INSERT INTO categories (id, name, description, icon_url, sort_order, enabled, created_at, updated_at, deleted) VALUES
(1, '中式菜品', '传统中式菜品', '/images/categories/chinese.png', 1, true, NOW(), NOW(), false),
(2, '西式菜品', '西式菜品和快餐', '/images/categories/western.png', 2, true, NOW(), NOW(), false),
(3, '日韩料理', '日式和韩式料理', '/images/categories/japanese.png', 3, true, NOW(), NOW(), false),
(4, '饮品甜品', '各类饮品和甜品', '/images/categories/drinks.png', 4, true, NOW(), NOW(), false),
(5, '小食零食', '小食和零食', '/images/categories/snacks.png', 5, true, NOW(), NOW(), false);

-- 插入子分类
INSERT INTO categories (id, name, description, parent_id, sort_order, enabled, created_at, updated_at, deleted) VALUES
(6, '川菜', '四川菜系', 1, 1, true, NOW(), NOW(), false),
(7, '粤菜', '广东菜系', 1, 2, true, NOW(), NOW(), false),
(8, '湘菜', '湖南菜系', 1, 3, true, NOW(), NOW(), false),
(9, '汉堡', '各式汉堡', 2, 1, true, NOW(), NOW(), false),
(10, '披萨', '意式披萨', 2, 2, true, NOW(), NOW(), false),
(11, '寿司', '日式寿司', 3, 1, true, NOW(), NOW(), false),
(12, '拉面', '日式拉面', 3, 2, true, NOW(), NOW(), false),
(13, '奶茶', '各式奶茶', 4, 1, true, NOW(), NOW(), false),
(14, '咖啡', '现磨咖啡', 4, 2, true, NOW(), NOW(), false);

-- 插入管理员用户
INSERT INTO users (id, username, password, email, real_name, enabled, email_verified, created_at, updated_at, deleted) VALUES
(1, 'admin', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9lBaLzfzUOoqIQi', '<EMAIL>', '系统管理员', true, true, NOW(), NOW(), false);

INSERT INTO user_roles (user_id, role) VALUES (1, 'ADMIN');

-- 插入测试商家用户
INSERT INTO users (id, username, password, email, real_name, phone, enabled, email_verified, created_at, updated_at, deleted) VALUES
(2, 'merchant1', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9lBaLzfzUOoqIQi', '<EMAIL>', '张三', '13800138001', true, true, NOW(), NOW(), false),
(3, 'merchant2', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9lBaLzfzUOoqIQi', '<EMAIL>', '李四', '13800138002', true, true, NOW(), NOW(), false);

INSERT INTO user_roles (user_id, role) VALUES (2, 'MERCHANT'), (3, 'MERCHANT');

-- 插入测试客户用户
INSERT INTO users (id, username, password, email, real_name, phone, address, enabled, email_verified, created_at, updated_at, deleted) VALUES
(4, 'customer1', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9lBaLzfzUOoqIQi', '<EMAIL>', '王五', '13800138003', '北京市朝阳区xxx街道', true, true, NOW(), NOW(), false),
(5, 'customer2', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9lBaLzfzUOoqIQi', '<EMAIL>', '赵六', '13800138004', '上海市浦东新区xxx路', true, true, NOW(), NOW(), false);

INSERT INTO user_roles (user_id, role) VALUES (4, 'CUSTOMER'), (5, 'CUSTOMER');

-- 插入商家信息
INSERT INTO merchants (id, user_id, name, description, address, phone, status, opening_hours, delivery_fee, min_order_amount, enabled, created_at, updated_at, deleted) VALUES
(1, 2, '川味小厨', '正宗川菜，麻辣鲜香', '北京市海淀区中关村大街1号', '010-12345678', 'APPROVED', '09:00-22:00', 5.00, 20.00, true, NOW(), NOW(), false),
(2, 3, '意式风情', '正宗意大利菜，新鲜食材', '上海市徐汇区淮海中路100号', '021-87654321', 'APPROVED', '10:00-23:00', 8.00, 30.00, true, NOW(), NOW(), false);

-- 插入商品信息
INSERT INTO meals (id, merchant_id, category_id, name, description, price, image_url, status, stock, spicy_level, preparation_time, enabled, created_at, updated_at, deleted) VALUES
(1, 1, 6, '麻婆豆腐', '经典川菜，嫩滑豆腐配麻辣肉末', 28.00, '/images/meals/mapo-tofu.jpg', 'AVAILABLE', 100, 2, 15, true, NOW(), NOW(), false),
(2, 1, 6, '宫保鸡丁', '传统川菜，鸡丁花生米爽脆可口', 32.00, '/images/meals/kungpao-chicken.jpg', 'AVAILABLE', 80, 1, 20, true, NOW(), NOW(), false),
(3, 1, 6, '水煮鱼', '麻辣鲜香，鱼肉嫩滑', 58.00, '/images/meals/boiled-fish.jpg', 'AVAILABLE', 50, 3, 25, true, NOW(), NOW(), false),
(4, 2, 10, '玛格丽特披萨', '经典意式披萨，番茄芝士罗勒', 45.00, '/images/meals/margherita-pizza.jpg', 'AVAILABLE', 60, 0, 18, true, NOW(), NOW(), false),
(5, 2, 10, '意式肉酱面', '传统博洛尼亚肉酱配意面', 38.00, '/images/meals/bolognese-pasta.jpg', 'AVAILABLE', 70, 0, 22, true, NOW(), NOW(), false);

-- 插入商品标签
INSERT INTO meal_tags (meal_id, tag) VALUES
(1, '经典'), (1, '下饭'), (1, '素食'),
(2, '经典'), (2, '下饭'), (2, '鸡肉'),
(3, '招牌'), (3, '麻辣'), (3, '鱼类'),
(4, '经典'), (4, '素食'), (4, '意式'),
(5, '经典'), (5, '意面'), (5, '肉类');
