# PowerShell script to fix encoding issues by removing Chinese characters and fixing syntax errors

Write-Host "Starting to fix encoding and syntax issues..."

# Get all Java files in the src directory
$javaFiles = Get-ChildItem -Path "src" -Filter "*.java" -Recurse

foreach ($file in $javaFiles) {
    Write-Host "Processing: $($file.FullName)"
    
    try {
        # Read the file content with UTF-8 encoding
        $content = Get-Content -Path $file.FullName -Encoding UTF8 -Raw
        
        # Remove or replace problematic Chinese characters with English equivalents
        $content = $content -replace '用户', 'User'
        $content = $content -replace '商家', 'Merchant'
        $content = $content -replace '管理员', 'Admin'
        $content = $content -replace '订单', 'Order'
        $content = $content -replace '菜品', 'Meal'
        $content = $content -replace '评价', 'Rating'
        $content = $content -replace '推荐', 'Recommendation'
        $content = $content -replace '分类', 'Category'
        $content = $content -replace '数据', 'Data'
        $content = $content -replace '统计', 'Statistics'
        $content = $content -replace '图表', 'Chart'
        $content = $content -replace '分析', 'Analytics'
        $content = $content -replace '报告', 'Report'
        $content = $content -replace '配置', 'Config'
        $content = $content -replace '服务', 'Service'
        $content = $content -replace '控制器', 'Controller'
        $content = $content -replace '实体', 'Entity'
        $content = $content -replace '仓库', 'Repository'
        $content = $content -replace '缓存', 'Cache'
        $content = $content -replace '密码', 'Password'
        $content = $content -replace '邮箱', 'Email'
        $content = $content -replace '验证', 'Validation'
        $content = $content -replace '令牌', 'Token'
        $content = $content -replace '重置', 'Reset'
        $content = $content -replace '忘记', 'Forgot'
        $content = $content -replace '请求', 'Request'
        $content = $content -replace '响应', 'Response'
        $content = $content -replace '状态', 'Status'
        $content = $content -replace '待处理', 'PENDING'
        $content = $content -replace '已确认', 'CONFIRMED'
        $content = $content -replace '准备中', 'PREPARING'
        $content = $content -replace '已完成', 'COMPLETED'
        $content = $content -replace '已取消', 'CANCELLED'
        $content = $content -replace '配送中', 'DELIVERING'
        
        # Fix common syntax issues
        $content = $content -replace '，', ','
        $content = $content -replace '；', ';'
        $content = $content -replace '"', '"'
        $content = $content -replace '"', '"'
        $content = $content -replace ''', "'"
        $content = $content -replace ''', "'"
        
        # Write the modified content back to the file with UTF-8 encoding
        [System.IO.File]::WriteAllText($file.FullName, $content, [System.Text.Encoding]::UTF8)
        
        Write-Host "Fixed: $($file.FullName)"
    }
    catch {
        Write-Host "Error processing $($file.FullName): $($_.Exception.Message)"
    }
}

Write-Host "Finished fixing encoding and syntax issues!"
