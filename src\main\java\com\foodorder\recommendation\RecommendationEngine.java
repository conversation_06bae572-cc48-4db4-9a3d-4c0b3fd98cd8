package com.foodorder.recommendation;

import com.foodorder.entity.Meal;
import com.foodorder.entity.User;

import java.util.List;

/**
 * 推荐引擎接口
 */
public interface RecommendationEngine {
    
    /**
     * 获取个性化推荐
     * @param user 用户
     * @param limit 推荐数量
     * @return 推荐的餐饮列表
     */
    List<Meal> getPersonalizedRecommendations(User user, int limit);
    
    /**
     * 获取实时推荐（基于最近行为）
     * @param user 用户
     * @param limit 推荐数量
     * @return 推荐的餐饮列表
     */
    List<Meal> getRealtimeRecommendations(User user, int limit);
    
    /**
     * 获取相似商品推荐
     * @param meal 基准商品
     * @param limit 推荐数量
     * @return 相似的餐饮列表
     */
    List<Meal> getSimilarMeals(Meal meal, int limit);
    
    /**
     * 获取热门推荐
     * @param limit 推荐数量
     * @return 热门餐饮列表
     */
    List<Meal> getPopularRecommendations(int limit);
    
    /**
     * 获取基于分类的推荐
     * @param user 用户
     * @param categoryId 分类ID
     * @param limit 推荐数量
     * @return 推荐的餐饮列表
     */
    List<Meal> getCategoryBasedRecommendations(User user, Long categoryId, int limit);
    
    /**
     * 获取协同过滤推荐
     * @param user 用户
     * @param limit 推荐数量
     * @return 推荐的餐饮列表
     */
    List<Meal> getCollaborativeFilteringRecommendations(User user, int limit);
    
    /**
     * 获取内容过滤推荐
     * @param user 用户
     * @param limit 推荐数量
     * @return 推荐的餐饮列表
     */
    List<Meal> getContentBasedRecommendations(User user, int limit);
    
    /**
     * 获取混合推荐（结合多种算法）
     * @param user 用户
     * @param limit 推荐数量
     * @return 推荐的餐饮列表
     */
    List<Meal> getHybridRecommendations(User user, int limit);
}
