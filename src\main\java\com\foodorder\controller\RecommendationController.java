package com.foodorder.controller;

import com.foodorder.common.response.ApiResponse;
import com.foodorder.dto.meal.MealDto;
import com.foodorder.service.RecommendationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/recommendations")
@Tag(name = "推荐系统", description = "智能推荐相关接口")
public class RecommendationController {
    
    @Autowired
    private RecommendationService recommendationService;
    
    @GetMapping("/homepage")
    @Operation(summary = "获取首页推荐", description = "获取首页推荐商品（未登录用户返回热门推荐，已登录用户返回个性化推荐）")
    public ResponseEntity<ApiResponse<List<MealDto>>> getHomepageRecommendations(
            @Parameter(description = "推荐数量") @RequestParam(defaultValue = "10") int limit) {
        
        List<MealDto> recommendations = recommendationService.getHomepageRecommendations(limit);
        return ResponseEntity.ok(ApiResponse.success(recommendations));
    }
    
    @GetMapping("/personalized")
    @PreAuthorize("hasRole('CUSTOMER')")
    @Operation(summary = "获取个性化推荐", description = "基于用户历史行为的个性化推荐")
    public ResponseEntity<ApiResponse<List<MealDto>>> getPersonalizedRecommendations(
            @RequestParam(defaultValue = "10") int limit) {
        
        List<MealDto> recommendations = recommendationService.getPersonalizedRecommendations(limit);
        return ResponseEntity.ok(ApiResponse.success(recommendations));
    }
    
    @GetMapping("/realtime")
    @PreAuthorize("hasRole('CUSTOMER')")
    @Operation(summary = "获取实时推荐", description = "基于用户最近行为的实时推荐")
    public ResponseEntity<ApiResponse<List<MealDto>>> getRealtimeRecommendations(
            @RequestParam(defaultValue = "10") int limit) {
        
        List<MealDto> recommendations = recommendationService.getRealtimeRecommendations(limit);
        return ResponseEntity.ok(ApiResponse.success(recommendations));
    }
    
    @GetMapping("/similar/{mealId}")
    @Operation(summary = "获取相似商品推荐", description = "获取与指定商品相似的其他商品")
    public ResponseEntity<ApiResponse<List<MealDto>>> getSimilarMeals(
            @PathVariable Long mealId,
            @RequestParam(defaultValue = "5") int limit) {
        
        List<MealDto> recommendations = recommendationService.getSimilarMeals(mealId, limit);
        return ResponseEntity.ok(ApiResponse.success(recommendations));
    }
    
    @GetMapping("/popular")
    @Operation(summary = "获取热门推荐", description = "获取当前热门商品推荐")
    public ResponseEntity<ApiResponse<List<MealDto>>> getPopularRecommendations(
            @RequestParam(defaultValue = "10") int limit) {
        
        List<MealDto> recommendations = recommendationService.getPopularRecommendations(limit);
        return ResponseEntity.ok(ApiResponse.success(recommendations));
    }
    
    @GetMapping("/category/{categoryId}")
    @PreAuthorize("hasRole('CUSTOMER')")
    @Operation(summary = "获取分类推荐", description = "基于指定分类的个性化推荐")
    public ResponseEntity<ApiResponse<List<MealDto>>> getCategoryBasedRecommendations(
            @PathVariable Long categoryId,
            @RequestParam(defaultValue = "10") int limit) {
        
        List<MealDto> recommendations = recommendationService.getCategoryBasedRecommendations(categoryId, limit);
        return ResponseEntity.ok(ApiResponse.success(recommendations));
    }
    
    @GetMapping("/collaborative")
    @PreAuthorize("hasRole('CUSTOMER')")
    @Operation(summary = "获取协同过滤推荐", description = "基于相似用户偏好的协同过滤推荐")
    public ResponseEntity<ApiResponse<List<MealDto>>> getCollaborativeFilteringRecommendations(
            @RequestParam(defaultValue = "10") int limit) {
        
        List<MealDto> recommendations = recommendationService.getCollaborativeFilteringRecommendations(limit);
        return ResponseEntity.ok(ApiResponse.success(recommendations));
    }
    
    @GetMapping("/content-based")
    @PreAuthorize("hasRole('CUSTOMER')")
    @Operation(summary = "获取内容推荐", description = "基于商品内容特征的推荐")
    public ResponseEntity<ApiResponse<List<MealDto>>> getContentBasedRecommendations(
            @RequestParam(defaultValue = "10") int limit) {
        
        List<MealDto> recommendations = recommendationService.getContentBasedRecommendations(limit);
        return ResponseEntity.ok(ApiResponse.success(recommendations));
    }
    
    @GetMapping("/hybrid")
    @PreAuthorize("hasRole('CUSTOMER')")
    @Operation(summary = "获取混合推荐", description = "结合多种推荐算法的混合推荐")
    public ResponseEntity<ApiResponse<List<MealDto>>> getHybridRecommendations(
            @RequestParam(defaultValue = "10") int limit) {
        
        List<MealDto> recommendations = recommendationService.getHybridRecommendations(limit);
        return ResponseEntity.ok(ApiResponse.success(recommendations));
    }
    
    @GetMapping("/new-user")
    @Operation(summary = "获取新用户推荐", description = "针对新用户的推荐策略")
    public ResponseEntity<ApiResponse<List<MealDto>>> getNewUserRecommendations(
            @RequestParam(defaultValue = "10") int limit) {
        
        List<MealDto> recommendations = recommendationService.getNewUserRecommendations(limit);
        return ResponseEntity.ok(ApiResponse.success(recommendations));
    }
    
    @GetMapping("/behavior-based")
    @PreAuthorize("hasRole('CUSTOMER')")
    @Operation(summary = "获取行为推荐", description = "基于用户行为模式的推荐")
    public ResponseEntity<ApiResponse<List<MealDto>>> getBehaviorBasedRecommendations(
            @RequestParam(defaultValue = "10") int limit) {
        
        List<MealDto> recommendations = recommendationService.getBehaviorBasedRecommendations(limit);
        return ResponseEntity.ok(ApiResponse.success(recommendations));
    }
}
