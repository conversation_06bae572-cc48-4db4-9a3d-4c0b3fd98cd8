package com.foodorder.dto.meal;

import java.math.BigDecimal;
import java.util.List;

public class MealSearchRequest {
    
    private String keyword;
    private Long categoryId;
    private Long merchantId;
    private BigDecimal minPrice;
    private BigDecimal maxPrice;
    private Integer spicyLevel;
    private List<String> tags;
    private String sortBy = "id"; // id, price, rating, salesCount
    private String sortDirection = "asc"; // asc, desc
    
    public MealSearchRequest() {}
    
    public String getKeyword() {
        return keyword;
    }
    
    public void setKeyword(String keyword) {
        this.keyword = keyword;
    }
    
    public Long getCategoryId() {
        return categoryId;
    }
    
    public void setCategoryId(Long categoryId) {
        this.categoryId = categoryId;
    }
    
    public Long getMerchantId() {
        return merchantId;
    }
    
    public void setMerchantId(Long merchantId) {
        this.merchantId = merchantId;
    }
    
    public BigDecimal getMinPrice() {
        return minPrice;
    }
    
    public void setMinPrice(BigDecimal minPrice) {
        this.minPrice = minPrice;
    }
    
    public BigDecimal getMaxPrice() {
        return maxPrice;
    }
    
    public void setMaxPrice(BigDecimal maxPrice) {
        this.maxPrice = maxPrice;
    }
    
    public Integer getSpicyLevel() {
        return spicyLevel;
    }
    
    public void setSpicyLevel(Integer spicyLevel) {
        this.spicyLevel = spicyLevel;
    }
    
    public List<String> getTags() {
        return tags;
    }
    
    public void setTags(List<String> tags) {
        this.tags = tags;
    }
    
    public String getSortBy() {
        return sortBy;
    }
    
    public void setSortBy(String sortBy) {
        this.sortBy = sortBy;
    }
    
    public String getSortDirection() {
        return sortDirection;
    }
    
    public void setSortDirection(String sortDirection) {
        this.sortDirection = sortDirection;
    }
}
