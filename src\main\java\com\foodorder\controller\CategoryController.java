package com.foodorder.controller;

import com.foodorder.common.response.ApiResponse;
import com.foodorder.dto.category.CategoryDto;
import com.foodorder.service.CategoryService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/categories")
@Tag(name = "分类管理", description = "餐饮分类相关接口")
public class CategoryController {
    
    @Autowired
    private CategoryService categoryService;
    
    @GetMapping("/{id}")
    @Operation(summary = "获取分类详情", description = "根据ID获取分类详细信息")
    public ResponseEntity<ApiResponse<CategoryDto>> getCategoryById(@PathVariable Long id) {
        CategoryDto category = categoryService.getCategoryById(id);
        return ResponseEntity.ok(ApiResponse.success(category));
    }
    
    @GetMapping("/root")
    @Operation(summary = "获取根分类", description = "获取所有根级分类及其子分类")
    public ResponseEntity<ApiResponse<List<CategoryDto>>> getRootCategories() {
        List<CategoryDto> categories = categoryService.getRootCategories();
        return ResponseEntity.ok(ApiResponse.success(categories));
    }
    
    @GetMapping("/{parentId}/children")
    @Operation(summary = "获取子分类", description = "根据父分类ID获取子分类列表")
    public ResponseEntity<ApiResponse<List<CategoryDto>>> getSubCategories(@PathVariable Long parentId) {
        List<CategoryDto> categories = categoryService.getSubCategories(parentId);
        return ResponseEntity.ok(ApiResponse.success(categories));
    }
    
    @GetMapping
    @Operation(summary = "获取所有分类", description = "获取所有启用的分类（扁平列表）")
    public ResponseEntity<ApiResponse<List<CategoryDto>>> getAllCategories() {
        List<CategoryDto> categories = categoryService.getAllCategories();
        return ResponseEntity.ok(ApiResponse.success(categories));
    }
    
    @GetMapping("/tree")
    @Operation(summary = "获取分类树", description = "获取完整的分类树结构")
    public ResponseEntity<ApiResponse<List<CategoryDto>>> getCategoryTree() {
        List<CategoryDto> categories = categoryService.getCategoryTree();
        return ResponseEntity.ok(ApiResponse.success(categories));
    }
}
