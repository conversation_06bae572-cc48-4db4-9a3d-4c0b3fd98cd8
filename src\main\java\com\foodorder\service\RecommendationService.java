package com.foodorder.service;

import com.foodorder.common.exception.BusinessException;
import com.foodorder.dto.meal.MealDto;
import com.foodorder.entity.Meal;
import com.foodorder.entity.User;
import com.foodorder.recommendation.RecommendationEngine;
import com.foodorder.recommendation.abtest.ABTestManager;
import com.foodorder.recommendation.logging.RecommendationLogger;
import com.foodorder.repository.MealRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Service
public class RecommendationService {
    
    private static final Logger logger = LoggerFactory.getLogger(RecommendationService.class);
    
    @Autowired
    private RecommendationEngine recommendationEngine;
    
    @Autowired
    private MealRepository mealRepository;
    
    @Autowired
    private AuthService authService;

    @Autowired
    private ABTestManager abTestManager;

    @Autowired
    private RecommendationLogger recommendationLogger;
    
    /**
     * 获取个性化推荐
     */
    public List<MealDto> getPersonalizedRecommendations(int limit) {
        User currentUser = authService.getCurrentUser();
        if (currentUser == null) {
            throw new BusinessException("用户未登录");
        }
        
        List<Meal> recommendations = recommendationEngine.getPersonalizedRecommendations(currentUser, limit);
        return recommendations.stream()
            .map(MealDto::fromMeal)
            .collect(Collectors.toList());
    }
    
    /**
     * 获取实时推荐
     */
    public List<MealDto> getRealtimeRecommendations(int limit) {
        User currentUser = authService.getCurrentUser();
        if (currentUser == null) {
            throw new BusinessException("用户未登录");
        }
        
        List<Meal> recommendations = recommendationEngine.getRealtimeRecommendations(currentUser, limit);
        return recommendations.stream()
            .map(MealDto::fromMeal)
            .collect(Collectors.toList());
    }
    
    /**
     * 获取相似商品推荐
     */
    public List<MealDto> getSimilarMeals(Long mealId, int limit) {
        Meal meal = mealRepository.findById(mealId)
            .orElseThrow(() -> new BusinessException("商品不存在"));
        
        List<Meal> recommendations = recommendationEngine.getSimilarMeals(meal, limit);
        return recommendations.stream()
            .map(MealDto::fromMeal)
            .collect(Collectors.toList());
    }
    
    /**
     * 获取热门推荐
     */
    @Cacheable(value = "recommendations", key = "'popular_' + #limit")
    public List<MealDto> getPopularRecommendations(int limit) {
        List<Meal> recommendations = recommendationEngine.getPopularRecommendations(limit);
        return recommendations.stream()
            .map(MealDto::fromMeal)
            .collect(Collectors.toList());
    }
    
    /**
     * 获取基于分类的推荐
     */
    public List<MealDto> getCategoryBasedRecommendations(Long categoryId, int limit) {
        User currentUser = authService.getCurrentUser();
        if (currentUser == null) {
            throw new BusinessException("用户未登录");
        }
        
        List<Meal> recommendations = recommendationEngine.getCategoryBasedRecommendations(currentUser, categoryId, limit);
        return recommendations.stream()
            .map(MealDto::fromMeal)
            .collect(Collectors.toList());
    }
    
    /**
     * 获取协同过滤推荐
     */
    public List<MealDto> getCollaborativeFilteringRecommendations(int limit) {
        User currentUser = authService.getCurrentUser();
        if (currentUser == null) {
            throw new BusinessException("用户未登录");
        }
        
        List<Meal> recommendations = recommendationEngine.getCollaborativeFilteringRecommendations(currentUser, limit);
        return recommendations.stream()
            .map(MealDto::fromMeal)
            .collect(Collectors.toList());
    }
    
    /**
     * 获取内容过滤推荐
     */
    public List<MealDto> getContentBasedRecommendations(int limit) {
        User currentUser = authService.getCurrentUser();
        if (currentUser == null) {
            throw new BusinessException("用户未登录");
        }
        
        List<Meal> recommendations = recommendationEngine.getContentBasedRecommendations(currentUser, limit);
        return recommendations.stream()
            .map(MealDto::fromMeal)
            .collect(Collectors.toList());
    }
    
    /**
     * 获取混合推荐
     */
    public List<MealDto> getHybridRecommendations(int limit) {
        User currentUser = authService.getCurrentUser();
        if (currentUser == null) {
            throw new BusinessException("用户未登录");
        }
        
        List<Meal> recommendations = recommendationEngine.getHybridRecommendations(currentUser, limit);
        return recommendations.stream()
            .map(MealDto::fromMeal)
            .collect(Collectors.toList());
    }
    
    /**
     * 获取首页推荐（混合多种算法，支持A/B测试）
     */
    public List<MealDto> getHomepageRecommendations(int limit) {
        User currentUser = authService.getCurrentUser();

        if (currentUser == null) {
            // 未登录用户，返回热门推荐
            return getPopularRecommendations(limit);
        }

        // 已登录用户，使用A/B测试获取推荐
        List<Meal> recommendations = abTestManager.getRecommendationsWithABTest(
            currentUser, limit, "hybrid");

        return recommendations.stream()
            .map(MealDto::fromMeal)
            .collect(Collectors.toList());
    }
    
    /**
     * 获取新用户推荐
     */
    public List<MealDto> getNewUserRecommendations(int limit) {
        // 新用户推荐策略：热门商品 + 高评分商品
        List<Meal> popularMeals = recommendationEngine.getPopularRecommendations(limit / 2);
        List<Meal> topRatedMeals = mealRepository.findTopRatedMeals(
            org.springframework.data.domain.PageRequest.of(0, limit / 2)
        );
        
        // 合并并去重
        List<Meal> recommendations = new java.util.ArrayList<>(popularMeals);
        for (Meal meal : topRatedMeals) {
            if (!recommendations.contains(meal)) {
                recommendations.add(meal);
            }
        }
        
        return recommendations.stream()
            .limit(limit)
            .map(MealDto::fromMeal)
            .collect(Collectors.toList());
    }
    
    /**
     * 获取基于用户行为的推荐
     */
    public List<MealDto> getBehaviorBasedRecommendations(int limit) {
        User currentUser = authService.getCurrentUser();
        if (currentUser == null) {
            return getPopularRecommendations(limit);
        }
        
        // 根据用户的历史行为（订单、评分）生成推荐
        return getPersonalizedRecommendations(limit);
    }
}
