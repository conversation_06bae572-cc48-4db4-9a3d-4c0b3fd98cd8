package com.foodorder.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.DecimalMax;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.NotNull;

import java.math.BigDecimal;

@Entity
@Table(name = "ratings", indexes = {
    @Index(name = "idx_rating_user_id", columnList = "user_id"),
    @Index(name = "idx_rating_meal_id", columnList = "meal_id"),
    @Index(name = "idx_rating_merchant_id", columnList = "merchant_id"),
    @Index(name = "idx_rating_order_id", columnList = "order_id"),
    @Index(name = "idx_rating_score", columnList = "score")
}, uniqueConstraints = {
    @UniqueConstraint(name = "uk_user_meal_order", columnNames = {"user_id", "meal_id", "order_id"})
})
public class Rating extends BaseEntity {
    
    @NotNull(message = "用户不能为空")
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", nullable = false)
    private User user;
    
    @NotNull(message = "商品不能为空")
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "meal_id", nullable = false)
    private Meal meal;
    
    @NotNull(message = "商家不能为空")
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "merchant_id", nullable = false)
    private Merchant merchant;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "order_id")
    private Order order;
    
    @NotNull(message = "评分不能为空")
    @DecimalMin(value = "1.0", message = "评分不能低于1分")
    @DecimalMax(value = "5.0", message = "评分不能高于5分")
    @Column(name = "score", nullable = false, precision = 2, scale = 1)
    private BigDecimal score;
    
    @Column(name = "review", columnDefinition = "TEXT")
    private String review;
    
    @Column(name = "anonymous", nullable = false)
    private Boolean anonymous = false;
    
    @Column(name = "helpful_count", nullable = false)
    private Integer helpfulCount = 0;
    
    // 构造函数
    public Rating() {}
    
    public Rating(User user, Meal meal, Merchant merchant, BigDecimal score) {
        this.user = user;
        this.meal = meal;
        this.merchant = merchant;
        this.score = score;
    }
    
    public Rating(User user, Meal meal, Merchant merchant, Order order, BigDecimal score, String review) {
        this.user = user;
        this.meal = meal;
        this.merchant = merchant;
        this.order = order;
        this.score = score;
        this.review = review;
    }
    
    // 便利方法
    public void increaseHelpfulCount() {
        this.helpfulCount++;
    }
    
    public void decreaseHelpfulCount() {
        if (this.helpfulCount > 0) {
            this.helpfulCount--;
        }
    }
    
    // Getters and Setters
    public User getUser() {
        return user;
    }
    
    public void setUser(User user) {
        this.user = user;
    }
    
    public Meal getMeal() {
        return meal;
    }
    
    public void setMeal(Meal meal) {
        this.meal = meal;
    }
    
    public Merchant getMerchant() {
        return merchant;
    }
    
    public void setMerchant(Merchant merchant) {
        this.merchant = merchant;
    }
    
    public Order getOrder() {
        return order;
    }
    
    public void setOrder(Order order) {
        this.order = order;
    }
    
    public BigDecimal getScore() {
        return score;
    }
    
    public void setScore(BigDecimal score) {
        this.score = score;
    }
    
    public String getReview() {
        return review;
    }
    
    public void setReview(String review) {
        this.review = review;
    }
    
    public Boolean getAnonymous() {
        return anonymous;
    }
    
    public void setAnonymous(Boolean anonymous) {
        this.anonymous = anonymous;
    }
    
    public Integer getHelpfulCount() {
        return helpfulCount;
    }
    
    public void setHelpfulCount(Integer helpfulCount) {
        this.helpfulCount = helpfulCount;
    }
}
