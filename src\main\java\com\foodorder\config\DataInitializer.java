package com.foodorder.config;

import com.foodorder.service.DataImportService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;

@Component
@Profile({"dev", "default"}) // 只在开发环境运行
public class DataInitializer implements CommandLineRunner {
    
    private static final Logger logger = LoggerFactory.getLogger(DataInitializer.class);
    
    @Autowired
    private DataImportService dataImportService;
    
    @Override
    public void run(String... args) throws Exception {
        logger.info("开始初始化数据...");
        
        try {
            // 导入评分数据
            dataImportService.importRatingData();
            logger.info("数据初始化完成");
        } catch (Exception e) {
            logger.error("数据初始化失败", e);
        }
    }
}
