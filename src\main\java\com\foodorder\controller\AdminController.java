package com.foodorder.controller;

import com.foodorder.common.response.ApiResponse;
import com.foodorder.common.response.PageResponse;
import com.foodorder.dto.admin.SystemStatsDto;
import com.foodorder.dto.admin.UserManagementDto;
import com.foodorder.dto.merchant.MerchantDto;
import com.foodorder.entity.MerchantStatus;
import com.foodorder.entity.UserRole;
import com.foodorder.service.AdminService;
import com.foodorder.service.MerchantService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/admin")
@PreAuthorize("hasRole('ADMIN')")
@Tag(name = "管理员管理", description = "管理员功能相关接口")
public class AdminController {
    
    @Autowired
    private AdminService adminService;
    
    @Autowired
    private MerchantService merchantService;
    
    @GetMapping("/dashboard")
    @Operation(summary = "获取系统统计", description = "获取系统整体统计数据")
    public ResponseEntity<ApiResponse<SystemStatsDto>> getDashboardStats() {
        SystemStatsDto stats = adminService.getSystemStats();
        return ResponseEntity.ok(ApiResponse.success(stats));
    }
    
    // 用户管理
    @GetMapping("/users")
    @Operation(summary = "获取所有用户", description = "分页获取所有用户列表")
    public ResponseEntity<ApiResponse<PageResponse<UserManagementDto>>> getAllUsers(
            @Parameter(description = "页码，从0开始") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "20") int size) {
        
        Pageable pageable = PageRequest.of(page, size);
        Page<UserManagementDto> users = adminService.getAllUsers(pageable);
        return ResponseEntity.ok(ApiResponse.success(PageResponse.of(users)));
    }
    
    @GetMapping("/users/search")
    @Operation(summary = "搜索用户", description = "根据关键词搜索用户")
    public ResponseEntity<ApiResponse<PageResponse<UserManagementDto>>> searchUsers(
            @RequestParam String keyword,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {
        
        Pageable pageable = PageRequest.of(page, size);
        Page<UserManagementDto> users = adminService.searchUsers(keyword, pageable);
        return ResponseEntity.ok(ApiResponse.success(PageResponse.of(users)));
    }
    
    @GetMapping("/users/role/{role}")
    @Operation(summary = "按角色获取用户", description = "根据用户角色获取用户列表")
    public ResponseEntity<ApiResponse<PageResponse<UserManagementDto>>> getUsersByRole(
            @PathVariable UserRole role,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {
        
        Pageable pageable = PageRequest.of(page, size);
        Page<UserManagementDto> users = adminService.getUsersByRole(role, pageable);
        return ResponseEntity.ok(ApiResponse.success(PageResponse.of(users)));
    }
    
    @PostMapping("/users/{id}/enable")
    @Operation(summary = "启用用户", description = "启用指定用户账户")
    public ResponseEntity<ApiResponse<Void>> enableUser(@PathVariable Long id) {
        adminService.enableUser(id);
        return ResponseEntity.ok(ApiResponse.success("用户已启用"));
    }
    
    @PostMapping("/users/{id}/disable")
    @Operation(summary = "禁用用户", description = "禁用指定用户账户")
    public ResponseEntity<ApiResponse<Void>> disableUser(@PathVariable Long id) {
        adminService.disableUser(id);
        return ResponseEntity.ok(ApiResponse.success("用户已禁用"));
    }
    
    @PostMapping("/users/{id}/roles/{role}")
    @Operation(summary = "添加用户角色", description = "为用户添加指定角色")
    public ResponseEntity<ApiResponse<Void>> addRoleToUser(
            @PathVariable Long id,
            @PathVariable UserRole role) {
        
        adminService.addRoleToUser(id, role);
        return ResponseEntity.ok(ApiResponse.success("角色添加成功"));
    }
    
    @DeleteMapping("/users/{id}/roles/{role}")
    @Operation(summary = "移除用户角色", description = "移除用户的指定角色")
    public ResponseEntity<ApiResponse<Void>> removeRoleFromUser(
            @PathVariable Long id,
            @PathVariable UserRole role) {
        
        adminService.removeRoleFromUser(id, role);
        return ResponseEntity.ok(ApiResponse.success("角色移除成功"));
    }
    
    // 商家管理
    @GetMapping("/merchants")
    @Operation(summary = "获取所有商家", description = "分页获取所有商家列表")
    public ResponseEntity<ApiResponse<PageResponse<MerchantDto>>> getAllMerchants(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {
        
        Pageable pageable = PageRequest.of(page, size);
        Page<MerchantDto> merchants = merchantService.getAllMerchants(pageable);
        return ResponseEntity.ok(ApiResponse.success(PageResponse.of(merchants)));
    }
    
    @GetMapping("/merchants/status/{status}")
    @Operation(summary = "按状态获取商家", description = "根据商家状态获取商家列表")
    public ResponseEntity<ApiResponse<PageResponse<MerchantDto>>> getMerchantsByStatus(
            @PathVariable MerchantStatus status,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {
        
        Pageable pageable = PageRequest.of(page, size);
        Page<MerchantDto> merchants = merchantService.getMerchantsByStatus(status, pageable);
        return ResponseEntity.ok(ApiResponse.success(PageResponse.of(merchants)));
    }
    
    @GetMapping("/merchants/search")
    @Operation(summary = "搜索商家", description = "根据关键词搜索商家")
    public ResponseEntity<ApiResponse<PageResponse<MerchantDto>>> searchMerchants(
            @RequestParam String keyword,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {
        
        Pageable pageable = PageRequest.of(page, size);
        Page<MerchantDto> merchants = merchantService.searchMerchants(keyword, pageable);
        return ResponseEntity.ok(ApiResponse.success(PageResponse.of(merchants)));
    }
    
    @PostMapping("/merchants/{id}/approve")
    @Operation(summary = "审核通过商家", description = "审核通过商家申请")
    public ResponseEntity<ApiResponse<Void>> approveMerchant(@PathVariable Long id) {
        merchantService.approveMerchant(id);
        return ResponseEntity.ok(ApiResponse.success("商家审核通过"));
    }
    
    @PostMapping("/merchants/{id}/reject")
    @Operation(summary = "审核拒绝商家", description = "审核拒绝商家申请")
    public ResponseEntity<ApiResponse<Void>> rejectMerchant(@PathVariable Long id) {
        merchantService.rejectMerchant(id);
        return ResponseEntity.ok(ApiResponse.success("商家审核拒绝"));
    }
    
    @PostMapping("/merchants/{id}/suspend")
    @Operation(summary = "暂停商家", description = "暂停商家营业")
    public ResponseEntity<ApiResponse<Void>> suspendMerchant(@PathVariable Long id) {
        merchantService.suspendMerchant(id);
        return ResponseEntity.ok(ApiResponse.success("商家已暂停"));
    }
    
    @PostMapping("/merchants/{id}/enable")
    @Operation(summary = "启用商家", description = "启用商家账户")
    public ResponseEntity<ApiResponse<Void>> enableMerchant(@PathVariable Long id) {
        merchantService.enableMerchant(id);
        return ResponseEntity.ok(ApiResponse.success("商家已启用"));
    }
    
    @PostMapping("/merchants/{id}/disable")
    @Operation(summary = "禁用商家", description = "禁用商家账户")
    public ResponseEntity<ApiResponse<Void>> disableMerchant(@PathVariable Long id) {
        merchantService.disableMerchant(id);
        return ResponseEntity.ok(ApiResponse.success("商家已禁用"));
    }
}
