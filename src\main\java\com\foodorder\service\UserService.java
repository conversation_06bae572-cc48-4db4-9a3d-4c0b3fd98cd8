package com.foodorder.service;

import com.foodorder.common.exception.BusinessException;
import com.foodorder.common.exception.ResourceNotFoundException;
import com.foodorder.dto.user.UpdateProfileRequest;
import com.foodorder.dto.user.UserProfileDto;
import com.foodorder.entity.User;
import com.foodorder.entity.UserRole;
import com.foodorder.repository.UserRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.stream.Collectors;

@Service
public class UserService {
    
    private static final Logger logger = LoggerFactory.getLogger(UserService.class);
    
    @Autowired
    private UserRepository userRepository;
    
    @Autowired
    private AuthService authService;
    
    public UserProfileDto getCurrentUserProfile() {
        User currentUser = authService.getCurrentUser();
        if (currentUser == null) {
            throw new BusinessException("用户未登录");
        }
        return UserProfileDto.fromUser(currentUser);
    }
    
    @Transactional
    public UserProfileDto updateProfile(UpdateProfileRequest request) {
        User currentUser = authService.getCurrentUser();
        if (currentUser == null) {
            throw new BusinessException("用户未登录");
        }
        
        // 检查邮箱是否被其他用户使用
        if (StringUtils.hasText(request.getEmail()) && 
            !request.getEmail().equals(currentUser.getEmail())) {
            if (userRepository.existsByEmail(request.getEmail())) {
                throw new BusinessException("邮箱已被其他用户使用");
            }
            currentUser.setEmail(request.getEmail());
            currentUser.setEmailVerified(false); // 邮箱变更后需要重新验证
        }
        
        // 更新其他字段
        if (StringUtils.hasText(request.getRealName())) {
            currentUser.setRealName(request.getRealName());
        }
        if (StringUtils.hasText(request.getPhone())) {
            currentUser.setPhone(request.getPhone());
        }
        if (StringUtils.hasText(request.getAddress())) {
            currentUser.setAddress(request.getAddress());
        }
        if (StringUtils.hasText(request.getAvatarUrl())) {
            currentUser.setAvatarUrl(request.getAvatarUrl());
        }
        
        User savedUser = userRepository.save(currentUser);
        logger.info("用户更新个人信息: {}", savedUser.getUsername());
        
        return UserProfileDto.fromUser(savedUser);
    }
    
    public UserProfileDto getUserById(Long id) {
        User user = userRepository.findById(id)
            .orElseThrow(() -> new ResourceNotFoundException("用户不存在"));
        return UserProfileDto.fromUser(user);
    }
    
    public List<UserProfileDto> getUsersByRole(UserRole role) {
        List<User> users = userRepository.findByRole(role);
        return users.stream()
            .map(UserProfileDto::fromUser)
            .collect(Collectors.toList());
    }
    
    public Page<UserProfileDto> getUsersByRole(UserRole role, Pageable pageable) {
        Page<User> users = userRepository.findByRole(role, pageable);
        return users.map(UserProfileDto::fromUser);
    }
    
    public Page<UserProfileDto> searchUsers(String keyword, Pageable pageable) {
        Page<User> users = userRepository.searchUsers(keyword, pageable);
        return users.map(UserProfileDto::fromUser);
    }
    
    public Page<UserProfileDto> getAllUsers(Pageable pageable) {
        Page<User> users = userRepository.findAllActive(pageable);
        return users.map(UserProfileDto::fromUser);
    }
    
    @Transactional
    public void enableUser(Long userId) {
        User user = userRepository.findById(userId)
            .orElseThrow(() -> new ResourceNotFoundException("用户不存在"));
        
        user.setEnabled(true);
        userRepository.save(user);
        logger.info("启用用户: {}", user.getUsername());
    }
    
    @Transactional
    public void disableUser(Long userId) {
        User user = userRepository.findById(userId)
            .orElseThrow(() -> new ResourceNotFoundException("用户不存在"));
        
        user.setEnabled(false);
        userRepository.save(user);
        logger.info("禁用用户: {}", user.getUsername());
    }
    
    @Transactional
    public void addRoleToUser(Long userId, UserRole role) {
        User user = userRepository.findById(userId)
            .orElseThrow(() -> new ResourceNotFoundException("用户不存在"));
        
        user.addRole(role);
        userRepository.save(user);
        logger.info("为用户 {} 添加角色: {}", user.getUsername(), role);
    }
    
    @Transactional
    public void removeRoleFromUser(Long userId, UserRole role) {
        User user = userRepository.findById(userId)
            .orElseThrow(() -> new ResourceNotFoundException("用户不存在"));
        
        user.removeRole(role);
        userRepository.save(user);
        logger.info("为用户 {} 移除角色: {}", user.getUsername(), role);
    }
    
    public boolean existsByUsername(String username) {
        return userRepository.existsByUsername(username);
    }
    
    public boolean existsByEmail(String email) {
        return userRepository.existsByEmail(email);
    }
}
