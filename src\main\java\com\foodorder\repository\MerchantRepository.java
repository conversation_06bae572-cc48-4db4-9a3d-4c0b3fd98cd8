package com.foodorder.repository;

import com.foodorder.entity.Merchant;
import com.foodorder.entity.MerchantStatus;
import com.foodorder.entity.User;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface MerchantRepository extends JpaRepository<Merchant, Long> {
    
    Optional<Merchant> findByUser(User user);
    
    Optional<Merchant> findByUserId(Long userId);
    
    List<Merchant> findByStatus(MerchantStatus status);
    
    Page<Merchant> findByStatus(MerchantStatus status, Pageable pageable);
    
    List<Merchant> findByEnabledTrue();
    
    Page<Merchant> findByEnabledTrue(Pageable pageable);
    
    @Query("SELECT m FROM Merchant m WHERE m.status = 'APPROVED' AND m.enabled = true")
    List<Merchant> findActiveApprovedMerchants();
    
    @Query("SELECT m FROM Merchant m WHERE m.status = 'APPROVED' AND m.enabled = true")
    Page<Merchant> findActiveApprovedMerchants(Pageable pageable);
    
    @Query("SELECT m FROM Merchant m WHERE m.deleted = false AND " +
           "(LOWER(m.name) LIKE LOWER(CONCAT('%', :keyword, '%')) OR " +
           "LOWER(m.description) LIKE LOWER(CONCAT('%', :keyword, '%')) OR " +
           "LOWER(m.address) LIKE LOWER(CONCAT('%', :keyword, '%')))")
    Page<Merchant> searchMerchants(@Param("keyword") String keyword, Pageable pageable);
    
    @Query("SELECT m FROM Merchant m WHERE m.deleted = false")
    Page<Merchant> findAllActive(Pageable pageable);
    
    @Query("SELECT m FROM Merchant m WHERE m.status = 'APPROVED' AND m.enabled = true " +
           "ORDER BY m.rating DESC, m.orderCount DESC")
    List<Merchant> findTopRatedMerchants(Pageable pageable);
}
