# 订餐系统 (Food Order System)

基于Spring Boot开发的智能订餐系统，支持多用户角色、推荐算法和数据可视化。

## 功能特性

### 用户角色
- **订餐人（消费者）**: 浏览餐饮、下单购买、评分评价、获取个性化推荐
- **商家**: 店铺管理、商品管理、订单处理、数据统计
- **管理员**: 用户管理、商家审核、系统配置、推荐算法管理

### 核心功能
- 用户认证与权限管理
- 餐饮搜索与分类浏览
- 智能推荐系统（个性化推荐、实时推荐、相似推荐、热门推荐）
- 订单管理与状态跟踪
- 评分评价系统
- 数据统计与可视化
- 实时缓存优化

## 技术栈

### 后端
- **框架**: Spring Boot 3.2.0
- **数据库**: MySQL 8.0 / H2 (开发环境)
- **缓存**: Redis
- **安全**: Spring Security + JWT
- **文档**: Swagger/OpenAPI 3
- **构建工具**: Maven

### 前端
- **模板引擎**: Thymeleaf
- **样式框架**: Bootstrap 5
- **图表库**: Chart.js
- **JavaScript**: ES6+

## 快速开始

### 环境要求
- JDK 17+
- Maven 3.6+
- MySQL 8.0+ (生产环境)
- Redis 6.0+ (可选，用于缓存)

### 运行步骤

1. **克隆项目**
```bash
git clone <repository-url>
cd food-order-system
```

2. **配置数据库**
```bash
# 创建数据库
mysql -u root -p
CREATE DATABASE food_order_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

3. **配置应用**
```bash
# 复制配置文件
cp src/main/resources/application-dev.yml.example src/main/resources/application-dev.yml
# 编辑配置文件，修改数据库连接信息
```

4. **启动应用**
```bash
# 开发环境
mvn spring-boot:run -Dspring-boot.run.profiles=dev

# 或者打包运行
mvn clean package
java -jar target/food-order-system-0.0.1-SNAPSHOT.jar --spring.profiles.active=dev
```

5. **访问应用**
- 应用地址: http://localhost:8080/api
- API文档: http://localhost:8080/api/swagger-ui.html
- H2控制台: http://localhost:8080/api/h2-console (开发环境)

## 项目结构

```
src/main/java/com/foodorder/
├── config/                 # 配置类
├── common/                 # 通用组件
│   ├── constants/         # 常量定义
│   ├── exception/         # 异常处理
│   ├── response/          # 响应封装
│   └── utils/             # 工具类
├── entity/                # 实体类
├── repository/            # 数据访问层
├── service/               # 业务逻辑层
├── controller/            # 控制器层
├── dto/                   # 数据传输对象
├── security/              # 安全配置
└── recommendation/        # 推荐系统
```

## API文档

启动应用后访问 `/swagger-ui.html` 查看完整的API文档。

### 主要接口
- `/auth/**` - 认证相关
- `/users/**` - 用户管理
- `/meals/**` - 餐饮管理
- `/orders/**` - 订单管理
- `/recommendations/**` - 推荐服务
- `/admin/**` - 管理员功能

## 推荐系统

### 推荐策略
1. **个性化推荐**: 基于用户历史行为的协同过滤
2. **实时推荐**: 基于用户最近评分的实时计算
3. **内容推荐**: 基于商品特征的相似度推荐
4. **热门推荐**: 基于全局统计的热门商品推荐

### 缓存策略
- 推荐结果缓存1小时
- 用户行为实时更新
- 热门商品定时刷新

## 部署说明

### Docker部署
```bash
# 构建镜像
docker build -t food-order-system .

# 运行容器
docker run -d -p 8080:8080 \
  -e SPRING_PROFILES_ACTIVE=prod \
  -e DATABASE_URL=***************************************** \
  -e DB_USERNAME=root \
  -e DB_PASSWORD=password \
  food-order-system
```

### 生产环境配置
1. 配置生产数据库连接
2. 设置JWT密钥
3. 配置Redis连接
4. 设置邮件服务
5. 配置文件上传路径

## 开发指南

### 代码规范
- 遵循阿里巴巴Java开发手册
- 使用统一的代码格式化配置
- 编写完整的单元测试
- 添加适当的注释和文档

### 提交规范
- feat: 新功能
- fix: 修复bug
- docs: 文档更新
- style: 代码格式调整
- refactor: 代码重构
- test: 测试相关
- chore: 构建过程或辅助工具的变动

## 许可证

MIT License

## 联系方式

如有问题或建议，请提交Issue或联系开发团队。
