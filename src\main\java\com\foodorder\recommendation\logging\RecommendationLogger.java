package com.foodorder.recommendation.logging;

import com.foodorder.entity.Meal;
import com.foodorder.entity.User;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 推荐系统日志记录器
 */
@Component
public class RecommendationLogger {
    
    private static final Logger logger = LoggerFactory.getLogger(RecommendationLogger.class);
    private static final String RECOMMENDATION_LOG_PREFIX = "rec_log:";
    private static final String USER_RECOMMENDATION_PREFIX = "user_rec:";
    private static final String ALGORITHM_STATS_PREFIX = "algo_stats:";
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    /**
     * 记录推荐请求
     */
    public void logRecommendationRequest(User user, String algorithm, int requestedCount, 
                                       List<Meal> recommendations, long executionTime) {
        try {
            RecommendationLogEntry logEntry = new RecommendationLogEntry(
                user.getId(),
                user.getUsername(),
                algorithm,
                requestedCount,
                recommendations.size(),
                recommendations.stream().map(Meal::getId).collect(Collectors.toList()),
                executionTime,
                LocalDateTime.now()
            );
            
            // 记录到Redis
            String logKey = RECOMMENDATION_LOG_PREFIX + user.getId() + ":" + System.currentTimeMillis();
            redisTemplate.opsForValue().set(logKey, logEntry, 7, TimeUnit.DAYS);
            
            // 记录用户最近推荐
            String userRecKey = USER_RECOMMENDATION_PREFIX + user.getId();
            redisTemplate.opsForList().leftPush(userRecKey, logEntry);
            redisTemplate.opsForList().trim(userRecKey, 0, 99); // 保留最近100条
            redisTemplate.expire(userRecKey, 30, TimeUnit.DAYS);
            
            // 更新算法统计
            updateAlgorithmStats(algorithm, recommendations.size(), executionTime);
            
            logger.info("推荐请求记录: 用户={}, 算法={}, 请求数量={}, 返回数量={}, 执行时间={}ms",
                user.getUsername(), algorithm, requestedCount, recommendations.size(), executionTime);
                
        } catch (Exception e) {
            logger.error("记录推荐请求失败", e);
        }
    }
    
    /**
     * 记录推荐点击
     */
    public void logRecommendationClick(User user, Long mealId, String algorithm, String source) {
        try {
            RecommendationClickEntry clickEntry = new RecommendationClickEntry(
                user.getId(),
                user.getUsername(),
                mealId,
                algorithm,
                source,
                LocalDateTime.now()
            );
            
            String clickKey = "rec_click:" + user.getId() + ":" + System.currentTimeMillis();
            redisTemplate.opsForValue().set(clickKey, clickEntry, 30, TimeUnit.DAYS);
            
            // 更新点击统计
            String statsKey = "click_stats:" + algorithm;
            redisTemplate.opsForHash().increment(statsKey, "total_clicks", 1);
            redisTemplate.expire(statsKey, 30, TimeUnit.DAYS);
            
            logger.info("推荐点击记录: 用户={}, 商品={}, 算法={}, 来源={}",
                user.getUsername(), mealId, algorithm, source);
                
        } catch (Exception e) {
            logger.error("记录推荐点击失败", e);
        }
    }
    
    /**
     * 记录推荐转化（购买）
     */
    public void logRecommendationConversion(User user, Long mealId, String algorithm, 
                                          String orderId, double amount) {
        try {
            RecommendationConversionEntry conversionEntry = new RecommendationConversionEntry(
                user.getId(),
                user.getUsername(),
                mealId,
                algorithm,
                orderId,
                amount,
                LocalDateTime.now()
            );
            
            String conversionKey = "rec_conversion:" + user.getId() + ":" + System.currentTimeMillis();
            redisTemplate.opsForValue().set(conversionKey, conversionEntry, 30, TimeUnit.DAYS);
            
            // 更新转化统计
            String statsKey = "conversion_stats:" + algorithm;
            redisTemplate.opsForHash().increment(statsKey, "total_conversions", 1);
            redisTemplate.opsForHash().increment(statsKey, "total_amount", amount);
            redisTemplate.expire(statsKey, 30, TimeUnit.DAYS);
            
            logger.info("推荐转化记录: 用户={}, 商品={}, 算法={}, 订单={}, 金额={}",
                user.getUsername(), mealId, algorithm, orderId, amount);
                
        } catch (Exception e) {
            logger.error("记录推荐转化失败", e);
        }
    }
    
    /**
     * 获取用户推荐历史
     */
    public List<RecommendationLogEntry> getUserRecommendationHistory(Long userId, int limit) {
        try {
            String userRecKey = USER_RECOMMENDATION_PREFIX + userId;
            List<Object> history = redisTemplate.opsForList().range(userRecKey, 0, limit - 1);
            
            return history.stream()
                .filter(obj -> obj instanceof RecommendationLogEntry)
                .map(obj -> (RecommendationLogEntry) obj)
                .collect(Collectors.toList());
                
        } catch (Exception e) {
            logger.error("获取用户推荐历史失败", e);
            return List.of();
        }
    }
    
    /**
     * 获取算法统计信息
     */
    public Map<String, Object> getAlgorithmStats(String algorithm) {
        try {
            String statsKey = ALGORITHM_STATS_PREFIX + algorithm;
            return redisTemplate.opsForHash().entries(statsKey);
        } catch (Exception e) {
            logger.error("获取算法统计失败", e);
            return new HashMap<>();
        }
    }
    
    /**
     * 更新算法统计
     */
    private void updateAlgorithmStats(String algorithm, int recommendationCount, long executionTime) {
        try {
            String statsKey = ALGORITHM_STATS_PREFIX + algorithm;
            redisTemplate.opsForHash().increment(statsKey, "total_requests", 1);
            redisTemplate.opsForHash().increment(statsKey, "total_recommendations", recommendationCount);
            redisTemplate.opsForHash().increment(statsKey, "total_execution_time", executionTime);
            
            // 计算平均执行时间
            Long totalRequests = (Long) redisTemplate.opsForHash().get(statsKey, "total_requests");
            Long totalTime = (Long) redisTemplate.opsForHash().get(statsKey, "total_execution_time");
            if (totalRequests != null && totalTime != null && totalRequests > 0) {
                double avgTime = (double) totalTime / totalRequests;
                redisTemplate.opsForHash().put(statsKey, "avg_execution_time", avgTime);
            }
            
            redisTemplate.expire(statsKey, 30, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.error("更新算法统计失败", e);
        }
    }
    
    /**
     * 推荐日志条目
     */
    public static class RecommendationLogEntry {
        private Long userId;
        private String username;
        private String algorithm;
        private int requestedCount;
        private int returnedCount;
        private List<Long> recommendedMealIds;
        private long executionTime;
        private LocalDateTime timestamp;
        
        public RecommendationLogEntry() {}
        
        public RecommendationLogEntry(Long userId, String username, String algorithm,
                                    int requestedCount, int returnedCount, List<Long> recommendedMealIds,
                                    long executionTime, LocalDateTime timestamp) {
            this.userId = userId;
            this.username = username;
            this.algorithm = algorithm;
            this.requestedCount = requestedCount;
            this.returnedCount = returnedCount;
            this.recommendedMealIds = recommendedMealIds;
            this.executionTime = executionTime;
            this.timestamp = timestamp;
        }
        
        // Getters and Setters
        public Long getUserId() { return userId; }
        public void setUserId(Long userId) { this.userId = userId; }
        
        public String getUsername() { return username; }
        public void setUsername(String username) { this.username = username; }
        
        public String getAlgorithm() { return algorithm; }
        public void setAlgorithm(String algorithm) { this.algorithm = algorithm; }
        
        public int getRequestedCount() { return requestedCount; }
        public void setRequestedCount(int requestedCount) { this.requestedCount = requestedCount; }
        
        public int getReturnedCount() { return returnedCount; }
        public void setReturnedCount(int returnedCount) { this.returnedCount = returnedCount; }
        
        public List<Long> getRecommendedMealIds() { return recommendedMealIds; }
        public void setRecommendedMealIds(List<Long> recommendedMealIds) { this.recommendedMealIds = recommendedMealIds; }
        
        public long getExecutionTime() { return executionTime; }
        public void setExecutionTime(long executionTime) { this.executionTime = executionTime; }
        
        public LocalDateTime getTimestamp() { return timestamp; }
        public void setTimestamp(LocalDateTime timestamp) { this.timestamp = timestamp; }
    }
    
    /**
     * 推荐点击条目
     */
    public static class RecommendationClickEntry {
        private Long userId;
        private String username;
        private Long mealId;
        private String algorithm;
        private String source;
        private LocalDateTime timestamp;
        
        public RecommendationClickEntry() {}
        
        public RecommendationClickEntry(Long userId, String username, Long mealId,
                                      String algorithm, String source, LocalDateTime timestamp) {
            this.userId = userId;
            this.username = username;
            this.mealId = mealId;
            this.algorithm = algorithm;
            this.source = source;
            this.timestamp = timestamp;
        }
        
        // Getters and Setters
        public Long getUserId() { return userId; }
        public void setUserId(Long userId) { this.userId = userId; }
        
        public String getUsername() { return username; }
        public void setUsername(String username) { this.username = username; }
        
        public Long getMealId() { return mealId; }
        public void setMealId(Long mealId) { this.mealId = mealId; }
        
        public String getAlgorithm() { return algorithm; }
        public void setAlgorithm(String algorithm) { this.algorithm = algorithm; }
        
        public String getSource() { return source; }
        public void setSource(String source) { this.source = source; }
        
        public LocalDateTime getTimestamp() { return timestamp; }
        public void setTimestamp(LocalDateTime timestamp) { this.timestamp = timestamp; }
    }
    
    /**
     * 推荐转化条目
     */
    public static class RecommendationConversionEntry {
        private Long userId;
        private String username;
        private Long mealId;
        private String algorithm;
        private String orderId;
        private double amount;
        private LocalDateTime timestamp;
        
        public RecommendationConversionEntry() {}
        
        public RecommendationConversionEntry(Long userId, String username, Long mealId,
                                           String algorithm, String orderId, double amount,
                                           LocalDateTime timestamp) {
            this.userId = userId;
            this.username = username;
            this.mealId = mealId;
            this.algorithm = algorithm;
            this.orderId = orderId;
            this.amount = amount;
            this.timestamp = timestamp;
        }
        
        // Getters and Setters
        public Long getUserId() { return userId; }
        public void setUserId(Long userId) { this.userId = userId; }
        
        public String getUsername() { return username; }
        public void setUsername(String username) { this.username = username; }
        
        public Long getMealId() { return mealId; }
        public void setMealId(Long mealId) { this.mealId = mealId; }
        
        public String getAlgorithm() { return algorithm; }
        public void setAlgorithm(String algorithm) { this.algorithm = algorithm; }
        
        public String getOrderId() { return orderId; }
        public void setOrderId(String orderId) { this.orderId = orderId; }
        
        public double getAmount() { return amount; }
        public void setAmount(double amount) { this.amount = amount; }
        
        public LocalDateTime getTimestamp() { return timestamp; }
        public void setTimestamp(LocalDateTime timestamp) { this.timestamp = timestamp; }
    }
}
