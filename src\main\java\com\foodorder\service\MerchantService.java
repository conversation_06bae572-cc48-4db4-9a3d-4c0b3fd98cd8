package com.foodorder.service;

import com.foodorder.common.exception.BusinessException;
import com.foodorder.common.exception.ResourceNotFoundException;
import com.foodorder.dto.merchant.MerchantDto;
import com.foodorder.dto.merchant.MerchantRegisterRequest;
import com.foodorder.dto.merchant.UpdateMerchantRequest;
import com.foodorder.entity.Merchant;
import com.foodorder.entity.MerchantStatus;
import com.foodorder.entity.User;
import com.foodorder.entity.UserRole;
import com.foodorder.repository.MerchantRepository;
import com.foodorder.repository.UserRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
public class MerchantService {
    
    private static final Logger logger = LoggerFactory.getLogger(MerchantService.class);
    
    @Autowired
    private MerchantRepository merchantRepository;
    
    @Autowired
    private UserRepository userRepository;
    
    @Autowired
    private AuthService authService;
    
    @Transactional
    public MerchantDto registerMerchant(MerchantRegisterRequest request) {
        User currentUser = authService.getCurrentUser();
        if (currentUser == null) {
            throw new BusinessException("用户未登录");
        }
        
        // 检查用户是否已经是商家
        Optional<Merchant> existingMerchant = merchantRepository.findByUser(currentUser);
        if (existingMerchant.isPresent()) {
            throw new BusinessException("您已经注册过商家");
        }
        
        // 创建商家
        Merchant merchant = new Merchant();
        merchant.setUser(currentUser);
        merchant.setName(request.getName());
        merchant.setDescription(request.getDescription());
        merchant.setAddress(request.getAddress());
        merchant.setPhone(request.getPhone());
        merchant.setLogoUrl(request.getLogoUrl());
        merchant.setCoverImageUrl(request.getCoverImageUrl());
        merchant.setBusinessLicense(request.getBusinessLicense());
        merchant.setOpeningHours(request.getOpeningHours());
        merchant.setDeliveryFee(request.getDeliveryFee());
        merchant.setMinOrderAmount(request.getMinOrderAmount());
        merchant.setStatus(MerchantStatus.PENDING);
        
        Merchant savedMerchant = merchantRepository.save(merchant);
        
        // 为用户添加商家角色
        currentUser.addRole(UserRole.MERCHANT);
        userRepository.save(currentUser);
        
        logger.info("用户 {} 注册商家: {}", currentUser.getUsername(), merchant.getName());
        
        return MerchantDto.fromMerchant(savedMerchant);
    }
    
    public MerchantDto getCurrentMerchant() {
        User currentUser = authService.getCurrentUser();
        if (currentUser == null) {
            throw new BusinessException("用户未登录");
        }
        
        Merchant merchant = merchantRepository.findByUser(currentUser)
            .orElseThrow(() -> new BusinessException("您还不是商家，请先注册"));
        
        return MerchantDto.fromMerchant(merchant);
    }
    
    @Transactional
    @CacheEvict(value = "merchants", allEntries = true)
    public MerchantDto updateMerchant(UpdateMerchantRequest request) {
        User currentUser = authService.getCurrentUser();
        if (currentUser == null) {
            throw new BusinessException("用户未登录");
        }
        
        Merchant merchant = merchantRepository.findByUser(currentUser)
            .orElseThrow(() -> new BusinessException("您还不是商家，请先注册"));
        
        // 更新字段
        if (StringUtils.hasText(request.getName())) {
            merchant.setName(request.getName());
        }
        if (StringUtils.hasText(request.getDescription())) {
            merchant.setDescription(request.getDescription());
        }
        if (StringUtils.hasText(request.getAddress())) {
            merchant.setAddress(request.getAddress());
        }
        if (StringUtils.hasText(request.getPhone())) {
            merchant.setPhone(request.getPhone());
        }
        if (StringUtils.hasText(request.getLogoUrl())) {
            merchant.setLogoUrl(request.getLogoUrl());
        }
        if (StringUtils.hasText(request.getCoverImageUrl())) {
            merchant.setCoverImageUrl(request.getCoverImageUrl());
        }
        if (StringUtils.hasText(request.getBusinessLicense())) {
            merchant.setBusinessLicense(request.getBusinessLicense());
        }
        if (StringUtils.hasText(request.getOpeningHours())) {
            merchant.setOpeningHours(request.getOpeningHours());
        }
        if (request.getDeliveryFee() != null) {
            merchant.setDeliveryFee(request.getDeliveryFee());
        }
        if (request.getMinOrderAmount() != null) {
            merchant.setMinOrderAmount(request.getMinOrderAmount());
        }
        
        Merchant savedMerchant = merchantRepository.save(merchant);
        logger.info("商家信息更新: {}", merchant.getName());
        
        return MerchantDto.fromMerchant(savedMerchant);
    }
    
    public MerchantDto getMerchantById(Long id) {
        Merchant merchant = merchantRepository.findById(id)
            .orElseThrow(() -> new ResourceNotFoundException("商家不存在"));
        return MerchantDto.fromMerchant(merchant);
    }
    
    @Cacheable(value = "merchants", key = "'approved'")
    public List<MerchantDto> getApprovedMerchants() {
        List<Merchant> merchants = merchantRepository.findActiveApprovedMerchants();
        return merchants.stream()
            .map(MerchantDto::fromMerchant)
            .collect(Collectors.toList());
    }
    
    public Page<MerchantDto> getApprovedMerchants(Pageable pageable) {
        Page<Merchant> merchants = merchantRepository.findActiveApprovedMerchants(pageable);
        return merchants.map(MerchantDto::fromMerchant);
    }
    
    public Page<MerchantDto> getMerchantsByStatus(MerchantStatus status, Pageable pageable) {
        Page<Merchant> merchants = merchantRepository.findByStatus(status, pageable);
        return merchants.map(MerchantDto::fromMerchant);
    }
    
    public Page<MerchantDto> searchMerchants(String keyword, Pageable pageable) {
        Page<Merchant> merchants = merchantRepository.searchMerchants(keyword, pageable);
        return merchants.map(MerchantDto::fromMerchant);
    }
    
    public Page<MerchantDto> getAllMerchants(Pageable pageable) {
        Page<Merchant> merchants = merchantRepository.findAllActive(pageable);
        return merchants.map(MerchantDto::fromMerchant);
    }
    
    @Transactional
    @CacheEvict(value = "merchants", allEntries = true)
    public void approveMerchant(Long merchantId) {
        Merchant merchant = merchantRepository.findById(merchantId)
            .orElseThrow(() -> new ResourceNotFoundException("商家不存在"));
        
        merchant.setStatus(MerchantStatus.APPROVED);
        merchantRepository.save(merchant);
        logger.info("商家审核通过: {}", merchant.getName());
    }
    
    @Transactional
    @CacheEvict(value = "merchants", allEntries = true)
    public void rejectMerchant(Long merchantId) {
        Merchant merchant = merchantRepository.findById(merchantId)
            .orElseThrow(() -> new ResourceNotFoundException("商家不存在"));
        
        merchant.setStatus(MerchantStatus.REJECTED);
        merchantRepository.save(merchant);
        logger.info("商家审核拒绝: {}", merchant.getName());
    }
    
    @Transactional
    @CacheEvict(value = "merchants", allEntries = true)
    public void suspendMerchant(Long merchantId) {
        Merchant merchant = merchantRepository.findById(merchantId)
            .orElseThrow(() -> new ResourceNotFoundException("商家不存在"));
        
        merchant.setStatus(MerchantStatus.SUSPENDED);
        merchantRepository.save(merchant);
        logger.info("商家暂停营业: {}", merchant.getName());
    }
    
    @Transactional
    @CacheEvict(value = "merchants", allEntries = true)
    public void enableMerchant(Long merchantId) {
        Merchant merchant = merchantRepository.findById(merchantId)
            .orElseThrow(() -> new ResourceNotFoundException("商家不存在"));
        
        merchant.setEnabled(true);
        merchantRepository.save(merchant);
        logger.info("启用商家: {}", merchant.getName());
    }
    
    @Transactional
    @CacheEvict(value = "merchants", allEntries = true)
    public void disableMerchant(Long merchantId) {
        Merchant merchant = merchantRepository.findById(merchantId)
            .orElseThrow(() -> new ResourceNotFoundException("商家不存在"));
        
        merchant.setEnabled(false);
        merchantRepository.save(merchant);
        logger.info("禁用商家: {}", merchant.getName());
    }
}
