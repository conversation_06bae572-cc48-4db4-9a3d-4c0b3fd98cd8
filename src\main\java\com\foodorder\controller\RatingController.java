package com.foodorder.controller;

import com.foodorder.common.response.ApiResponse;
import com.foodorder.common.response.PageResponse;
import com.foodorder.dto.rating.CreateRatingRequest;
import com.foodorder.dto.rating.RatingDto;
import com.foodorder.service.RatingService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/ratings")
@Tag(name = "评分管理", description = "评分评价相关接口")
public class RatingController {
    
    @Autowired
    private RatingService ratingService;
    
    @PostMapping
    @PreAuthorize("hasRole('CUSTOMER')")
    @Operation(summary = "创建评分", description = "客户对商品进行评分")
    public ResponseEntity<ApiResponse<RatingDto>> createRating(@Valid @RequestBody CreateRatingRequest request) {
        RatingDto rating = ratingService.createRating(request);
        return ResponseEntity.ok(ApiResponse.success("评分成功", rating));
    }
    
    @GetMapping("/my")
    @PreAuthorize("hasRole('CUSTOMER')")
    @Operation(summary = "获取我的评分", description = "获取当前用户的评分列表")
    public ResponseEntity<ApiResponse<PageResponse<RatingDto>>> getMyRatings(
            @Parameter(description = "页码，从0开始") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "20") int size) {
        
        Pageable pageable = PageRequest.of(page, size);
        Page<RatingDto> ratings = ratingService.getUserRatings(pageable);
        return ResponseEntity.ok(ApiResponse.success(PageResponse.of(ratings)));
    }
    
    @GetMapping("/meal/{mealId}")
    @Operation(summary = "获取商品评分", description = "获取指定商品的评分列表")
    public ResponseEntity<ApiResponse<PageResponse<RatingDto>>> getMealRatings(
            @PathVariable Long mealId,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {
        
        Pageable pageable = PageRequest.of(page, size);
        Page<RatingDto> ratings = ratingService.getMealRatings(mealId, pageable);
        return ResponseEntity.ok(ApiResponse.success(PageResponse.of(ratings)));
    }
    
    @GetMapping("/meal/{mealId}/reviews")
    @Operation(summary = "获取商品评价", description = "获取指定商品的文字评价")
    public ResponseEntity<ApiResponse<PageResponse<RatingDto>>> getMealReviews(
            @PathVariable Long mealId,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {
        
        Pageable pageable = PageRequest.of(page, size);
        Page<RatingDto> ratings = ratingService.getMealRatingsWithReviews(mealId, pageable);
        return ResponseEntity.ok(ApiResponse.success(PageResponse.of(ratings)));
    }
    
    @GetMapping("/merchant/{merchantId}")
    @Operation(summary = "获取商家评分", description = "获取指定商家的评分列表")
    public ResponseEntity<ApiResponse<PageResponse<RatingDto>>> getMerchantRatings(
            @PathVariable Long merchantId,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {
        
        Pageable pageable = PageRequest.of(page, size);
        Page<RatingDto> ratings = ratingService.getMerchantRatings(merchantId, pageable);
        return ResponseEntity.ok(ApiResponse.success(PageResponse.of(ratings)));
    }
    
    @GetMapping("/reviews")
    @Operation(summary = "获取所有评价", description = "获取所有包含文字评价的评分")
    public ResponseEntity<ApiResponse<PageResponse<RatingDto>>> getAllReviews(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {
        
        Pageable pageable = PageRequest.of(page, size);
        Page<RatingDto> ratings = ratingService.getRatingsWithReviews(pageable);
        return ResponseEntity.ok(ApiResponse.success(PageResponse.of(ratings)));
    }
    
    @GetMapping("/user/{userId}/recent")
    @Operation(summary = "获取用户最近评分", description = "获取指定用户的最近评分（用于推荐系统）")
    public ResponseEntity<ApiResponse<List<RatingDto>>> getUserRecentRatings(
            @PathVariable Long userId,
            @RequestParam(defaultValue = "10") int limit) {
        
        List<RatingDto> ratings = ratingService.getUserRecentRatings(userId, limit);
        return ResponseEntity.ok(ApiResponse.success(ratings));
    }
    
    @PostMapping("/{id}/helpful")
    @PreAuthorize("hasRole('CUSTOMER') or hasRole('MERCHANT') or hasRole('ADMIN')")
    @Operation(summary = "标记评价有用", description = "标记评价为有用")
    public ResponseEntity<ApiResponse<Void>> markRatingHelpful(@PathVariable Long id) {
        ratingService.markRatingHelpful(id);
        return ResponseEntity.ok(ApiResponse.success("标记成功"));
    }
}
