package com.foodorder.service;

import com.foodorder.common.exception.BusinessException;
import com.foodorder.common.exception.ResourceNotFoundException;
import com.foodorder.dto.admin.SystemStatsDto;
import com.foodorder.dto.admin.UserManagementDto;
import com.foodorder.entity.*;
import com.foodorder.repository.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class AdminService {
    
    private static final Logger logger = LoggerFactory.getLogger(AdminService.class);
    
    @Autowired
    private UserRepository userRepository;
    
    @Autowired
    private MerchantRepository merchantRepository;
    
    @Autowired
    private MealRepository mealRepository;
    
    @Autowired
    private OrderRepository orderRepository;
    
    @Autowired
    private RatingRepository ratingRepository;
    
    @Autowired
    private CategoryRepository categoryRepository;
    
    @Autowired
    private AuthService authService;
    
    @Cacheable(value = "systemStats", key = "'dashboard'")
    public SystemStatsDto getSystemStats() {
        SystemStatsDto stats = new SystemStatsDto();
        
        // 用户统计
        stats.setTotalUsers(userRepository.count());
        stats.setTotalCustomers((long) userRepository.findByRole(UserRole.CUSTOMER).size());
        stats.setTotalMerchants((long) userRepository.findByRole(UserRole.MERCHANT).size());
        stats.setTotalAdmins((long) userRepository.findByRole(UserRole.ADMIN).size());
        
        // 活跃用户（最近30天有登录记录）
        LocalDateTime thirtyDaysAgo = LocalDateTime.now().minusDays(30);
        long activeUsers = userRepository.findAll().stream()
            .filter(user -> user.getLastLoginAt() != null && user.getLastLoginAt().isAfter(thirtyDaysAgo))
            .count();
        stats.setActiveUsers(activeUsers);
        
        // 商家统计
        stats.setTotalMerchantsCount(merchantRepository.count());
        stats.setApprovedMerchants((long) merchantRepository.findByStatus(MerchantStatus.APPROVED).size());
        stats.setPendingMerchants((long) merchantRepository.findByStatus(MerchantStatus.PENDING).size());
        stats.setRejectedMerchants((long) merchantRepository.findByStatus(MerchantStatus.REJECTED).size());
        stats.setSuspendedMerchants((long) merchantRepository.findByStatus(MerchantStatus.SUSPENDED).size());
        
        // 商品统计
        stats.setTotalMeals(mealRepository.count());
        stats.setAvailableMeals((long) mealRepository.findByStatus(MealStatus.AVAILABLE).size());
        stats.setSoldOutMeals((long) mealRepository.findByStatus(MealStatus.SOLD_OUT).size());
        stats.setDiscontinuedMeals((long) mealRepository.findByStatus(MealStatus.DISCONTINUED).size());
        
        // 订单统计
        stats.setTotalOrders(orderRepository.count());
        stats.setPendingOrders(orderRepository.countByStatus(OrderStatus.PENDING));
        stats.setCompletedOrders(orderRepository.countByStatus(OrderStatus.DELIVERED));
        stats.setCancelledOrders(orderRepository.countByStatus(OrderStatus.CANCELLED));
        
        // 收入统计
        List<Order> completedOrders = orderRepository.findByStatus(OrderStatus.DELIVERED);
        BigDecimal totalRevenue = completedOrders.stream()
            .map(Order::getTotalAmount)
            .reduce(BigDecimal.ZERO, BigDecimal::add);
        stats.setTotalRevenue(totalRevenue);
        
        // 今日收入
        LocalDateTime todayStart = LocalDateTime.now().withHour(0).withMinute(0).withSecond(0);
        LocalDateTime todayEnd = LocalDateTime.now().withHour(23).withMinute(59).withSecond(59);
        List<Order> todayOrders = orderRepository.findByCreatedAtBetween(todayStart, todayEnd);
        BigDecimal todayRevenue = todayOrders.stream()
            .filter(order -> order.getStatus() == OrderStatus.DELIVERED)
            .map(Order::getTotalAmount)
            .reduce(BigDecimal.ZERO, BigDecimal::add);
        stats.setTodayRevenue(todayRevenue);
        
        // 评分统计
        stats.setTotalRatings(ratingRepository.count());
        List<Rating> allRatings = ratingRepository.findAll();
        if (!allRatings.isEmpty()) {
            BigDecimal avgRating = allRatings.stream()
                .map(Rating::getScore)
                .reduce(BigDecimal.ZERO, BigDecimal::add)
                .divide(BigDecimal.valueOf(allRatings.size()), 2, java.math.RoundingMode.HALF_UP);
            stats.setAverageRating(avgRating);
        }
        
        long ratingsWithReviews = allRatings.stream()
            .filter(rating -> rating.getReview() != null && !rating.getReview().trim().isEmpty())
            .count();
        stats.setRatingsWithReviews(ratingsWithReviews);
        
        // 分类统计
        stats.setTotalCategories(categoryRepository.count());
        stats.setRootCategories((long) categoryRepository.findByParentIsNull().size());
        stats.setSubCategories((long) categoryRepository.findByParentIsNotNull().size());
        
        // 趋势数据
        stats.setUserRegistrationTrend(getUserRegistrationTrend());
        stats.setOrderTrend(getOrderTrend());
        stats.setRevenueTrend(getRevenueTrend());
        
        return stats;
    }
    
    public Page<UserManagementDto> getAllUsers(Pageable pageable) {
        Page<User> users = userRepository.findAllActive(pageable);
        return users.map(user -> {
            UserManagementDto dto = UserManagementDto.fromUser(user);
            // 添加统计信息
            dto.setOrderCount(orderRepository.countByCustomer(user));
            dto.setRatingCount(ratingRepository.countByUser(user));
            return dto;
        });
    }
    
    public Page<UserManagementDto> searchUsers(String keyword, Pageable pageable) {
        Page<User> users = userRepository.searchUsers(keyword, pageable);
        return users.map(user -> {
            UserManagementDto dto = UserManagementDto.fromUser(user);
            dto.setOrderCount(orderRepository.countByCustomer(user));
            dto.setRatingCount(ratingRepository.countByUser(user));
            return dto;
        });
    }
    
    public Page<UserManagementDto> getUsersByRole(UserRole role, Pageable pageable) {
        Page<User> users = userRepository.findByRole(role, pageable);
        return users.map(user -> {
            UserManagementDto dto = UserManagementDto.fromUser(user);
            dto.setOrderCount(orderRepository.countByCustomer(user));
            dto.setRatingCount(ratingRepository.countByUser(user));
            return dto;
        });
    }
    
    @Transactional
    public void enableUser(Long userId) {
        User user = userRepository.findById(userId)
            .orElseThrow(() -> new ResourceNotFoundException("用户不存在"));
        
        user.setEnabled(true);
        userRepository.save(user);
        logger.info("管理员启用用户: {}", user.getUsername());
    }
    
    @Transactional
    public void disableUser(Long userId) {
        User user = userRepository.findById(userId)
            .orElseThrow(() -> new ResourceNotFoundException("用户不存在"));
        
        // 不能禁用管理员账户
        if (user.hasRole(UserRole.ADMIN)) {
            throw new BusinessException("不能禁用管理员账户");
        }
        
        user.setEnabled(false);
        userRepository.save(user);
        logger.info("管理员禁用用户: {}", user.getUsername());
    }
    
    @Transactional
    public void addRoleToUser(Long userId, UserRole role) {
        User user = userRepository.findById(userId)
            .orElseThrow(() -> new ResourceNotFoundException("用户不存在"));
        
        user.addRole(role);
        userRepository.save(user);
        logger.info("管理员为用户 {} 添加角色: {}", user.getUsername(), role);
    }
    
    @Transactional
    public void removeRoleFromUser(Long userId, UserRole role) {
        User user = userRepository.findById(userId)
            .orElseThrow(() -> new ResourceNotFoundException("用户不存在"));
        
        // 不能移除管理员的管理员角色
        if (role == UserRole.ADMIN && user.hasRole(UserRole.ADMIN)) {
            User currentUser = authService.getCurrentUser();
            if (currentUser != null && currentUser.getId().equals(userId)) {
                throw new BusinessException("不能移除自己的管理员角色");
            }
        }
        
        user.removeRole(role);
        userRepository.save(user);
        logger.info("管理员为用户 {} 移除角色: {}", user.getUsername(), role);
    }
    
    private Map<String, Long> getUserRegistrationTrend() {
        Map<String, Long> trend = new HashMap<>();
        LocalDateTime now = LocalDateTime.now();
        
        for (int i = 6; i >= 0; i--) {
            LocalDateTime date = now.minusDays(i);
            LocalDateTime dayStart = date.withHour(0).withMinute(0).withSecond(0);
            LocalDateTime dayEnd = date.withHour(23).withMinute(59).withSecond(59);
            
            long count = userRepository.findAll().stream()
                .filter(user -> user.getCreatedAt().isAfter(dayStart) && user.getCreatedAt().isBefore(dayEnd))
                .count();
            
            trend.put(date.toLocalDate().toString(), count);
        }
        
        return trend;
    }
    
    private Map<String, Long> getOrderTrend() {
        Map<String, Long> trend = new HashMap<>();
        LocalDateTime now = LocalDateTime.now();
        
        for (int i = 6; i >= 0; i--) {
            LocalDateTime date = now.minusDays(i);
            LocalDateTime dayStart = date.withHour(0).withMinute(0).withSecond(0);
            LocalDateTime dayEnd = date.withHour(23).withMinute(59).withSecond(59);
            
            List<Order> dayOrders = orderRepository.findByCreatedAtBetween(dayStart, dayEnd);
            trend.put(date.toLocalDate().toString(), (long) dayOrders.size());
        }
        
        return trend;
    }
    
    private Map<String, BigDecimal> getRevenueTrend() {
        Map<String, BigDecimal> trend = new HashMap<>();
        LocalDateTime now = LocalDateTime.now();
        
        for (int i = 6; i >= 0; i--) {
            LocalDateTime date = now.minusDays(i);
            LocalDateTime dayStart = date.withHour(0).withMinute(0).withSecond(0);
            LocalDateTime dayEnd = date.withHour(23).withMinute(59).withSecond(59);
            
            List<Order> dayOrders = orderRepository.findByCreatedAtBetween(dayStart, dayEnd);
            BigDecimal dayRevenue = dayOrders.stream()
                .filter(order -> order.getStatus() == OrderStatus.DELIVERED)
                .map(Order::getTotalAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
            
            trend.put(date.toLocalDate().toString(), dayRevenue);
        }
        
        return trend;
    }
}
