package com.foodorder.dto.order;

import com.foodorder.entity.OrderItem;

import java.math.BigDecimal;

public class OrderItemDto {
    
    private Long id;
    private Long mealId;
    private String mealName;
    private String mealImageUrl;
    private BigDecimal price;
    private Integer quantity;
    private BigDecimal subtotal;
    private String notes;
    
    public OrderItemDto() {}
    
    public OrderItemDto(OrderItem orderItem) {
        this.id = orderItem.getId();
        this.mealId = orderItem.getMeal().getId();
        this.mealName = orderItem.getMealName();
        this.mealImageUrl = orderItem.getMealImageUrl();
        this.price = orderItem.getPrice();
        this.quantity = orderItem.getQuantity();
        this.subtotal = orderItem.getSubtotal();
        this.notes = orderItem.getNotes();
    }
    
    public static OrderItemDto fromOrderItem(OrderItem orderItem) {
        return new OrderItemDto(orderItem);
    }
    
    // Getters and Setters
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public Long getMealId() {
        return mealId;
    }
    
    public void setMealId(Long mealId) {
        this.mealId = mealId;
    }
    
    public String getMealName() {
        return mealName;
    }
    
    public void setMealName(String mealName) {
        this.mealName = mealName;
    }
    
    public String getMealImageUrl() {
        return mealImageUrl;
    }
    
    public void setMealImageUrl(String mealImageUrl) {
        this.mealImageUrl = mealImageUrl;
    }
    
    public BigDecimal getPrice() {
        return price;
    }
    
    public void setPrice(BigDecimal price) {
        this.price = price;
    }
    
    public Integer getQuantity() {
        return quantity;
    }
    
    public void setQuantity(Integer quantity) {
        this.quantity = quantity;
    }
    
    public BigDecimal getSubtotal() {
        return subtotal;
    }
    
    public void setSubtotal(BigDecimal subtotal) {
        this.subtotal = subtotal;
    }
    
    public String getNotes() {
        return notes;
    }
    
    public void setNotes(String notes) {
        this.notes = notes;
    }
}
